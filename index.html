<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mass Premier Courts - Documentation</title>
    <link rel="stylesheet" href="documentation-styles.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- PDF Generation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  </head>
  <body>
    <!-- Navigation Sidebar -->
    <nav class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <h2><i class="fas fa-basketball-ball"></i> Mass Premier Courts</h2>
        <button class="sidebar-toggle" id="sidebarToggle">
          <i class="fas fa-bars"></i>
        </button>
      </div>

      <div class="sidebar-content">
        <ul class="nav-menu" id="navMenu">
          <li>
            <a href="#system-overview" class="nav-link active"
              ><i class="fas fa-info-circle"></i> System Overview</a
            >
          </li>
          <li>
            <a href="#user-roles" class="nav-link"
              ><i class="fas fa-users"></i> User Roles & Capabilities</a
            >
          </li>
          <li>
            <a href="#data-flow" class="nav-link"
              ><i class="fas fa-project-diagram"></i> Data Flow Architecture</a
            >
          </li>
          <li>
            <a href="#payment-system" class="nav-link"
              ><i class="fas fa-credit-card"></i> Payment System Structure</a
            >
          </li>
          <li>
            <a href="#invitation-system" class="nav-link"
              ><i class="fas fa-envelope"></i> Invitation System Workflows</a
            >
          </li>
          <li>
            <a href="#multi-role" class="nav-link"
              ><i class="fas fa-user-shield"></i> Multi-Role User Management</a
            >
          </li>
          <li>
            <a href="#program-management" class="nav-link"
              ><i class="fas fa-calendar-alt"></i> Program Management
              Workflows</a
            >
          </li>
          <li>
            <a href="#team-management" class="nav-link"
              ><i class="fas fa-users-cog"></i> Team Management System</a
            >
          </li>
          <li>
            <a href="#reporting" class="nav-link"
              ><i class="fas fa-chart-bar"></i> Reporting & Analytics</a
            >
          </li>
          <li>
            <a href="#security" class="nav-link"
              ><i class="fas fa-shield-alt"></i> Security & Access Control</a
            >
          </li>
        </ul>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
      <header class="content-header">
        <div class="header-controls">
          <button class="mobile-menu-toggle" id="mobileMenuToggle">
            <i class="fas fa-bars"></i>
          </button>
          <button class="pdf-export-btn" id="pdfExportBtn">
            <i class="fas fa-file-pdf"></i>
            Export to PDF
          </button>
        </div>
        <h1>Mass Premier Courts - Sports Program Management System</h1>
        <p class="subtitle">Comprehensive Documentation & Technical Guide</p>
      </header>

      <!-- System Overview Section -->
      <section id="system-overview" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-info-circle"></i> System Overview</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            Mass Premier Courts is a comprehensive sports program management
            platform built with Laravel 11. The system manages sports programs,
            team registrations, player enrollments, coaching assignments, and
            payment processing for sports organizations.
          </p>

          <div class="tech-stack">
            <h3><i class="fas fa-layer-group"></i> Core Technologies</h3>
            <div class="tech-grid">
              <div class="tech-item">
                <div class="tech-icon backend">
                  <i class="fab fa-php"></i>
                </div>
                <div class="tech-info">
                  <h4>Backend</h4>
                  <p>Laravel 11 (PHP 8.2+)</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon frontend">
                  <i class="fab fa-js"></i>
                </div>
                <div class="tech-info">
                  <h4>Frontend</h4>
                  <p>Livewire 3.6, Blade Templates, JavaScript</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon database">
                  <i class="fas fa-database"></i>
                </div>
                <div class="tech-info">
                  <h4>Database</h4>
                  <p>MySQL/SQLite</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon payment">
                  <i class="fab fa-stripe"></i>
                </div>
                <div class="tech-info">
                  <h4>Payment Processing</h4>
                  <p>Stripe API with Laravel Cashier</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon auth">
                  <i class="fas fa-key"></i>
                </div>
                <div class="tech-info">
                  <h4>Authentication</h4>
                  <p>Laravel Sanctum</p>
                </div>
              </div>
              <div class="tech-item">
                <div class="tech-icon file">
                  <i class="fas fa-file-excel"></i>
                </div>
                <div class="tech-info">
                  <h4>File Management</h4>
                  <p>Laravel Excel (Maatwebsite)</p>
                </div>
              </div>
            </div>
          </div>

          <div class="system-features">
            <h3><i class="fas fa-star"></i> Key System Features</h3>
            <div class="features-grid">
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-users"></i>
                </div>
                <h4>Multi-Role User Management</h4>
                <p>
                  Comprehensive role-based access control for admins, guardians,
                  coaches, and players
                </p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-calendar-check"></i>
                </div>
                <h4>Program Management</h4>
                <p>
                  Create and manage various types of sports programs with
                  flexible scheduling
                </p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-credit-card"></i>
                </div>
                <h4>Payment Processing</h4>
                <p>
                  Integrated payment system with multiple payment options and
                  coupon support
                </p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-envelope-open"></i>
                </div>
                <h4>Invitation System</h4>
                <p>
                  Streamlined invitation workflow for team and program
                  participation
                </p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <h4>Reporting & Analytics</h4>
                <p>
                  Comprehensive reporting tools for program performance and
                  financial tracking
                </p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-shield-alt"></i>
                </div>
                <h4>Security & Access Control</h4>
                <p>
                  Robust security measures with role-based permissions and data
                  protection
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- User Roles & Capabilities Section -->
      <section id="user-roles" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-users"></i> User Roles & Capabilities</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            The Mass Premier Courts system supports four distinct user roles,
            each with specific capabilities and responsibilities. Understanding
            these roles is crucial for effective system management and user
            experience.
          </p>

          <!-- Role Overview Cards -->
          <div class="roles-overview">
            <h3><i class="fas fa-user-tie"></i> Role Overview</h3>
            <div class="roles-grid">
              <div class="role-card admin">
                <div class="role-header">
                  <div class="role-icon">
                    <i class="fas fa-crown"></i>
                  </div>
                  <h4>Admin</h4>
                  <span class="role-badge">System Administrator</span>
                </div>
                <p>Complete system oversight and management capabilities</p>
                <div class="role-stats">
                  <div class="stat">
                    <span class="stat-number">100%</span>
                    <span class="stat-label">Access</span>
                  </div>
                  <div class="stat">
                    <span class="stat-number">All</span>
                    <span class="stat-label">Features</span>
                  </div>
                </div>
              </div>

              <div class="role-card guardian">
                <div class="role-header">
                  <div class="role-icon">
                    <i class="fas fa-user-friends"></i>
                  </div>
                  <h4>Guardian</h4>
                  <span class="role-badge">Parent/Guardian</span>
                </div>
                <p>Manage family accounts and player registrations</p>
                <div class="role-stats">
                  <div class="stat">
                    <span class="stat-number">Family</span>
                    <span class="stat-label">Management</span>
                  </div>
                  <div class="stat">
                    <span class="stat-number">Payment</span>
                    <span class="stat-label">Processing</span>
                  </div>
                </div>
              </div>

              <div class="role-card coach">
                <div class="role-header">
                  <div class="role-icon">
                    <i class="fas fa-user-graduate"></i>
                  </div>
                  <h4>Coach</h4>
                  <span class="role-badge">Team Coach</span>
                </div>
                <p>Team management and player recruitment</p>
                <div class="role-stats">
                  <div class="stat">
                    <span class="stat-number">Team</span>
                    <span class="stat-label">Management</span>
                  </div>
                  <div class="stat">
                    <span class="stat-number">Player</span>
                    <span class="stat-label">Invitations</span>
                  </div>
                </div>
              </div>

              <div class="role-card player">
                <div class="role-header">
                  <div class="role-icon">
                    <i class="fas fa-running"></i>
                  </div>
                  <h4>Player</h4>
                  <span class="role-badge">Athlete</span>
                </div>
                <p>Profile management and program participation</p>
                <div class="role-stats">
                  <div class="stat">
                    <span class="stat-number">Profile</span>
                    <span class="stat-label">Management</span>
                  </div>
                  <div class="stat">
                    <span class="stat-number">View</span>
                    <span class="stat-label">Only</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Detailed Role Capabilities -->
          <div class="role-details">
            <h3><i class="fas fa-list-check"></i> Detailed Capabilities</h3>

            <!-- Admin Capabilities -->
            <div class="capability-section">
              <div class="capability-header admin">
                <h4><i class="fas fa-crown"></i> Admin Capabilities</h4>
                <p>Complete system oversight and management</p>
              </div>

              <div class="capabilities-grid">
                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-calendar-plus"></i>
                  </div>
                  <h5>Program Management</h5>
                  <ul>
                    <li>Create and manage sports programs</li>
                    <li>Set program parameters and pricing</li>
                    <li>Configure registration periods</li>
                    <li>Manage program status and visibility</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-users-cog"></i>
                  </div>
                  <h5>User Management</h5>
                  <ul>
                    <li>Create guardian and coach accounts</li>
                    <li>Manage player profiles</li>
                    <li>Merge duplicate accounts</li>
                    <li>Handle guardian-player relationships</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-trophy"></i>
                  </div>
                  <h5>Team Management</h5>
                  <ul>
                    <li>Create teams and assign coaches</li>
                    <li>Add teams to programs</li>
                    <li>Manage team rosters</li>
                    <li>Assign players to teams</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-credit-card"></i>
                  </div>
                  <h5>Financial Management</h5>
                  <ul>
                    <li>Monitor payment transactions</li>
                    <li>Manage guardian credits</li>
                    <li>Generate financial reports</li>
                    <li>Export payment data</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-envelope-open"></i>
                  </div>
                  <h5>Invitation System</h5>
                  <ul>
                    <li>Send program invitations to players</li>
                    <li>Invite players to specific teams</li>
                    <li>Manage tryout program invitations</li>
                    <li>Track invitation status</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-chart-bar"></i>
                  </div>
                  <h5>Reporting & Analytics</h5>
                  <ul>
                    <li>Generate user reports</li>
                    <li>Create program reports</li>
                    <li>Financial reporting</li>
                    <li>Export data to Excel</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Guardian Capabilities -->
            <div class="capability-section">
              <div class="capability-header guardian">
                <h4>
                  <i class="fas fa-user-friends"></i> Guardian Capabilities
                </h4>
                <p>Family management and player registration</p>
              </div>

              <div class="capabilities-grid">
                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-child"></i>
                  </div>
                  <h5>Player Management</h5>
                  <ul>
                    <li>Add new players to family account</li>
                    <li>Edit player information and profiles</li>
                    <li>Upload player photos</li>
                    <li>Manage player details</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-calendar-check"></i>
                  </div>
                  <h5>Program Registration</h5>
                  <ul>
                    <li>Register players for available programs</li>
                    <li>View program details and requirements</li>
                    <li>Check player eligibility</li>
                    <li>Manage waitlist positions</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-credit-card"></i>
                  </div>
                  <h5>Payment Processing</h5>
                  <ul>
                    <li>Process full payments for programs</li>
                    <li>Set up split payments</li>
                    <li>Configure recurring payments</li>
                    <li>Use guardian credits</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-network-wired"></i>
                  </div>
                  <h5>Guardian Network</h5>
                  <ul>
                    <li>Add additional guardians to family</li>
                    <li>Search and invite other guardians</li>
                    <li>Manage guardian relationships</li>
                    <li>Coordinate payments between guardians</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-envelope"></i>
                  </div>
                  <h5>Invitation Management</h5>
                  <ul>
                    <li>Receive and respond to player invitations</li>
                    <li>Accept or reject team invitations</li>
                    <li>View invitation details and requirements</li>
                    <li>Manage multiple player invitations</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-tachometer-alt"></i>
                  </div>
                  <h5>Dashboard Features</h5>
                  <ul>
                    <li>View current program enrollments</li>
                    <li>Monitor payment status</li>
                    <li>Track recurring payments</li>
                    <li>Access player profiles</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Coach Capabilities -->
            <div class="capability-section">
              <div class="capability-header coach">
                <h4><i class="fas fa-user-graduate"></i> Coach Capabilities</h4>
                <p>Team management and player recruitment</p>
              </div>

              <div class="capabilities-grid">
                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <h5>Team Management</h5>
                  <ul>
                    <li>Create new teams</li>
                    <li>Manage team information</li>
                    <li>Set team names and locations</li>
                    <li>Assign assistant coaches</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-user-plus"></i>
                  </div>
                  <h5>Player Invitations</h5>
                  <ul>
                    <li>Search for available players</li>
                    <li>Send invitations to players</li>
                    <li>Set payment amounts for invitations</li>
                    <li>Track invitation responses</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-handshake"></i>
                  </div>
                  <h5>Coach Collaboration</h5>
                  <ul>
                    <li>Invite other coaches to teams</li>
                    <li>Manage assistant coach relationships</li>
                    <li>Coordinate team responsibilities</li>
                    <li>Share team management duties</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-calendar-alt"></i>
                  </div>
                  <h5>Program Management</h5>
                  <ul>
                    <li>Register teams for programs</li>
                    <li>View team program schedules</li>
                    <li>Track team participation</li>
                    <li>Manage program registrations</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-tachometer-alt"></i>
                  </div>
                  <h5>Dashboard Features</h5>
                  <ul>
                    <li>View team rosters and players</li>
                    <li>Monitor invitation status</li>
                    <li>Track team programs</li>
                    <li>Manage team settings</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-user-info"></i>
                  </div>
                  <h5>Player Information</h5>
                  <ul>
                    <li>View player profiles</li>
                    <li>Access player contact information</li>
                    <li>Track player participation</li>
                    <li>Manage player assignments</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Player Capabilities -->
            <div class="capability-section">
              <div class="capability-header player">
                <h4><i class="fas fa-running"></i> Player Capabilities</h4>
                <p>Profile management and program participation</p>
              </div>

              <div class="capabilities-grid">
                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-user-circle"></i>
                  </div>
                  <h5>Profile Management</h5>
                  <ul>
                    <li>Cannot log in directly (managed by guardians)</li>
                    <li>Profile information stored and managed</li>
                    <li>Personal details maintained by guardians</li>
                    <li>Photo and information updates</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-calendar-check"></i>
                  </div>
                  <h5>Program Participation</h5>
                  <ul>
                    <li>Program participation tracked</li>
                    <li>Team membership recorded</li>
                    <li>Performance data collection</li>
                    <li>Attendance monitoring</li>
                  </ul>
                </div>

                <div class="capability-card">
                  <div class="capability-icon">
                    <i class="fas fa-trophy"></i>
                  </div>
                  <h5>Team Membership</h5>
                  <ul>
                    <li>Team assignments managed</li>
                    <li>Roster positions tracked</li>
                    <li>Team participation history</li>
                    <li>Coach assignments recorded</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Role Comparison Table -->
          <div class="role-comparison">
            <h3><i class="fas fa-table"></i> Role Comparison Matrix</h3>
            <div class="comparison-table-wrapper">
              <table class="comparison-table">
                <thead>
                  <tr>
                    <th>Feature</th>
                    <th>Admin</th>
                    <th>Guardian</th>
                    <th>Coach</th>
                    <th>Player</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Program Creation</td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                  </tr>
                  <tr>
                    <td>User Management</td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                  </tr>
                  <tr>
                    <td>Team Management</td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                  </tr>
                  <tr>
                    <td>Payment Processing</td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                  </tr>
                  <tr>
                    <td>Player Registration</td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                  </tr>
                  <tr>
                    <td>Invitation System</td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                  </tr>
                  <tr>
                    <td>Reporting</td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                  </tr>
                  <tr>
                    <td>Profile Management</td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-check text-success"></i>
                    </td>
                    <td>
                      <i class="fas fa-times text-muted"></i>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      <!-- Data Flow Architecture Section -->
      <section id="data-flow" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-project-diagram"></i> Data Flow Architecture</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            Understanding the data flow architecture is crucial for system
            development and maintenance. This section outlines how data moves
            through the Mass Premier Courts system, from user authentication to
            payment processing and program management.
          </p>

          <!-- Flow Overview -->
          <div class="flow-overview">
            <h3><i class="fas fa-sitemap"></i> System Flow Overview</h3>
            <div class="flow-diagram">
              <div class="flow-step">
                <div class="flow-icon">
                  <i class="fas fa-sign-in-alt"></i>
                </div>
                <div class="flow-content">
                  <h4>User Authentication</h4>
                  <p>
                    Login request with role verification and session management
                  </p>
                </div>
              </div>
              <div class="flow-arrow">
                <i class="fas fa-arrow-down"></i>
              </div>
              <div class="flow-step">
                <div class="flow-icon">
                  <i class="fas fa-users"></i>
                </div>
                <div class="flow-content">
                  <h4>Role-Based Access</h4>
                  <p>Current role determination and dashboard redirection</p>
                </div>
              </div>
              <div class="flow-arrow">
                <i class="fas fa-arrow-down"></i>
              </div>
              <div class="flow-step">
                <div class="flow-icon">
                  <i class="fas fa-cogs"></i>
                </div>
                <div class="flow-content">
                  <h4>System Operations</h4>
                  <p>
                    Program management, payments, invitations, and reporting
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Detailed Flow Diagrams -->
          <div class="flow-details">
            <h3><i class="fas fa-route"></i> Detailed Flow Diagrams</h3>

            <!-- Authentication Flow -->
            <div class="flow-section">
              <div class="flow-header">
                <h4>
                  <i class="fas fa-sign-in-alt"></i> User Authentication Flow
                </h4>
                <p>Complete authentication process with multi-role support</p>
              </div>

              <div class="flow-diagram-detailed">
                <div class="flow-node start">
                  <div class="node-icon">
                    <i class="fas fa-play"></i>
                  </div>
                  <div class="node-content">
                    <h5>Login Request</h5>
                    <p>User enters credentials</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node process">
                  <div class="node-icon">
                    <i class="fas fa-shield-alt"></i>
                  </div>
                  <div class="node-content">
                    <h5>Credential Validation</h5>
                    <p>Verify email/password</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node decision">
                  <div class="node-icon">
                    <i class="fas fa-question-circle"></i>
                  </div>
                  <div class="node-content">
                    <h5>Valid Credentials?</h5>
                    <p>Check authentication</p>
                  </div>
                </div>

                <div class="flow-branch">
                  <div class="branch-yes">
                    <div class="flow-connection">
                      <div class="connection-line"></div>
                      <div class="connection-arrow">
                        <i class="fas fa-arrow-right"></i>
                      </div>
                    </div>
                    <div class="flow-node process">
                      <div class="node-icon">
                        <i class="fas fa-user-tag"></i>
                      </div>
                      <div class="node-content">
                        <h5>Role Check</h5>
                        <p>Determine user roles</p>
                      </div>
                    </div>
                  </div>
                  <div class="branch-no">
                    <div class="flow-connection">
                      <div class="connection-line"></div>
                      <div class="connection-arrow">
                        <i class="fas fa-arrow-right"></i>
                      </div>
                    </div>
                    <div class="flow-node error">
                      <div class="node-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                      </div>
                      <div class="node-content">
                        <h5>Authentication Failed</h5>
                        <p>Show error message</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node end">
                  <div class="node-icon">
                    <i class="fas fa-home"></i>
                  </div>
                  <div class="node-content">
                    <h5>Dashboard Redirect</h5>
                    <p>Role-specific dashboard</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Program Registration Flow -->
            <div class="flow-section">
              <div class="flow-header">
                <h4>
                  <i class="fas fa-calendar-check"></i>
                  Program Registration Flow
                </h4>
                <p>
                  Complete registration process from program selection to
                  confirmation
                </p>
              </div>

              <div class="flow-diagram-detailed">
                <div class="flow-node start">
                  <div class="node-icon">
                    <i class="fas fa-search"></i>
                  </div>
                  <div class="node-content">
                    <h5>Program Selection</h5>
                    <p>Guardian selects program</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node process">
                  <div class="node-icon">
                    <i class="fas fa-user-check"></i>
                  </div>
                  <div class="node-content">
                    <h5>Player Eligibility Check</h5>
                    <p>Verify age, gender, requirements</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node decision">
                  <div class="node-icon">
                    <i class="fas fa-question-circle"></i>
                  </div>
                  <div class="node-content">
                    <h5>Player Eligible?</h5>
                    <p>Check all criteria</p>
                  </div>
                </div>

                <div class="flow-branch">
                  <div class="branch-yes">
                    <div class="flow-connection">
                      <div class="connection-line"></div>
                      <div class="connection-arrow">
                        <i class="fas fa-arrow-right"></i>
                      </div>
                    </div>
                    <div class="flow-node process">
                      <div class="node-icon">
                        <i class="fas fa-credit-card"></i>
                      </div>
                      <div class="node-content">
                        <h5>Payment Method Selection</h5>
                        <p>Choose payment type</p>
                      </div>
                    </div>
                  </div>
                  <div class="branch-no">
                    <div class="flow-connection">
                      <div class="connection-line"></div>
                      <div class="connection-arrow">
                        <i class="fas fa-arrow-right"></i>
                      </div>
                    </div>
                    <div class="flow-node error">
                      <div class="node-icon">
                        <i class="fas fa-ban"></i>
                      </div>
                      <div class="node-content">
                        <h5>Not Eligible</h5>
                        <p>Show eligibility message</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node process">
                  <div class="node-icon">
                    <i class="fas fa-cogs"></i>
                  </div>
                  <div class="node-content">
                    <h5>Payment Processing</h5>
                    <p>Stripe integration</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node end">
                  <div class="node-icon">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="node-content">
                    <h5>Registration Confirmation</h5>
                    <p>Player registered successfully</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Payment Processing Flow -->
            <div class="flow-section">
              <div class="flow-header">
                <h4>
                  <i class="fas fa-credit-card"></i> Payment Processing Flow
                </h4>
                <p>
                  Comprehensive payment workflow with multiple payment options
                </p>
              </div>

              <div class="flow-diagram-detailed">
                <div class="flow-node start">
                  <div class="node-icon">
                    <i class="fas fa-shopping-cart"></i>
                  </div>
                  <div class="node-content">
                    <h5>Payment Selection</h5>
                    <p>Choose payment method</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node process">
                  <div class="node-icon">
                    <i class="fas fa-calculator"></i>
                  </div>
                  <div class="node-content">
                    <h5>Amount Calculation</h5>
                    <p>Calculate total cost</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node process">
                  <div class="node-icon">
                    <i class="fas fa-link"></i>
                  </div>
                  <div class="node-content">
                    <h5>Stripe Integration</h5>
                    <p>Process payment</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node decision">
                  <div class="node-icon">
                    <i class="fas fa-question-circle"></i>
                  </div>
                  <div class="node-content">
                    <h5>Payment Successful?</h5>
                    <p>Verify transaction</p>
                  </div>
                </div>

                <div class="flow-branch">
                  <div class="branch-yes">
                    <div class="flow-connection">
                      <div class="connection-line"></div>
                      <div class="connection-arrow">
                        <i class="fas fa-arrow-right"></i>
                      </div>
                    </div>
                    <div class="flow-node end">
                      <div class="node-icon">
                        <i class="fas fa-check-circle"></i>
                      </div>
                      <div class="node-content">
                        <h5>Payment Confirmation</h5>
                        <p>Update registration</p>
                      </div>
                    </div>
                  </div>
                  <div class="branch-no">
                    <div class="flow-connection">
                      <div class="connection-line"></div>
                      <div class="connection-arrow">
                        <i class="fas fa-arrow-right"></i>
                      </div>
                    </div>
                    <div class="flow-node error">
                      <div class="node-icon">
                        <i class="fas fa-times-circle"></i>
                      </div>
                      <div class="node-content">
                        <h5>Payment Failed</h5>
                        <p>Show error message</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Invitation System Flow -->
            <div class="flow-section">
              <div class="flow-header">
                <h4>
                  <i class="fas fa-envelope-open"></i>
                  Invitation System Flow
                </h4>
                <p>Complete invitation workflow from creation to response</p>
              </div>

              <div class="flow-diagram-detailed">
                <div class="flow-node start">
                  <div class="node-icon">
                    <i class="fas fa-plus"></i>
                  </div>
                  <div class="node-content">
                    <h5>Invitation Creation</h5>
                    <p>Admin/Coach creates invitation</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node process">
                  <div class="node-icon">
                    <i class="fas fa-envelope"></i>
                  </div>
                  <div class="node-content">
                    <h5>Email Notification</h5>
                    <p>Send to guardian</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node process">
                  <div class="node-icon">
                    <i class="fas fa-user-check"></i>
                  </div>
                  <div class="node-content">
                    <h5>Guardian Response</h5>
                    <p>Accept or reject</p>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node decision">
                  <div class="node-icon">
                    <i class="fas fa-question-circle"></i>
                  </div>
                  <div class="node-content">
                    <h5>Invitation Accepted?</h5>
                    <p>Check response</p>
                  </div>
                </div>

                <div class="flow-branch">
                  <div class="branch-yes">
                    <div class="flow-connection">
                      <div class="connection-line"></div>
                      <div class="connection-arrow">
                        <i class="fas fa-arrow-right"></i>
                      </div>
                    </div>
                    <div class="flow-node process">
                      <div class="node-icon">
                        <i class="fas fa-credit-card"></i>
                      </div>
                      <div class="node-content">
                        <h5>Payment Processing</h5>
                        <p>Process payment</p>
                      </div>
                    </div>
                  </div>
                  <div class="branch-no">
                    <div class="flow-connection">
                      <div class="connection-line"></div>
                      <div class="connection-arrow">
                        <i class="fas fa-arrow-right"></i>
                      </div>
                    </div>
                    <div class="flow-node end">
                      <div class="node-icon">
                        <i class="fas fa-times"></i>
                      </div>
                      <div class="node-content">
                        <h5>Invitation Declined</h5>
                        <p>Mark as declined</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flow-connection">
                  <div class="connection-line"></div>
                  <div class="connection-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                </div>

                <div class="flow-node end">
                  <div class="node-icon">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="node-content">
                    <h5>Registration Completion</h5>
                    <p>Player registered</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Flow Summary -->
          <div class="flow-summary">
            <h3><i class="fas fa-chart-line"></i> Data Flow Summary</h3>
            <div class="summary-grid">
              <div class="summary-card">
                <div class="summary-icon">
                  <i class="fas fa-user-shield"></i>
                </div>
                <h4>Authentication Flow</h4>
                <p>
                  Secure login with role-based access control and session
                  management
                </p>
                <ul>
                  <li>Credential validation</li>
                  <li>Role determination</li>
                  <li>Dashboard redirection</li>
                </ul>
              </div>

              <div class="summary-card">
                <div class="summary-icon">
                  <i class="fas fa-calendar-plus"></i>
                </div>
                <h4>Registration Flow</h4>
                <p>
                  Program registration with eligibility checks and payment
                  processing
                </p>
                <ul>
                  <li>Program selection</li>
                  <li>Eligibility verification</li>
                  <li>Payment processing</li>
                </ul>
              </div>

              <div class="summary-card">
                <div class="summary-icon">
                  <i class="fas fa-credit-card"></i>
                </div>
                <h4>Payment Flow</h4>
                <p>
                  Multiple payment options with Stripe integration and
                  confirmation
                </p>
                <ul>
                  <li>Payment method selection</li>
                  <li>Amount calculation</li>
                  <li>Transaction confirmation</li>
                </ul>
              </div>

              <div class="summary-card">
                <div class="summary-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <h4>Invitation Flow</h4>
                <p>
                  Invitation system with email notifications and response
                  handling
                </p>
                <ul>
                  <li>Invitation creation</li>
                  <li>Email notification</li>
                  <li>Response processing</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Payment System Structure Section -->
      <section id="payment-system" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-credit-card"></i> Payment System Structure</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            The Mass Premier Courts payment system is a comprehensive solution
            that supports multiple payment types, coupon systems, credit
            management, and seamless Stripe integration. This section details
            the complete payment architecture and workflows.
          </p>

          <!-- Payment Types Overview -->
          <div class="payment-overview">
            <h3><i class="fas fa-list-ul"></i> Payment Types & Workflows</h3>
            <div class="payment-types-grid">
              <div class="payment-type-card full-payment">
                <div class="payment-type-header">
                  <div class="payment-icon">
                    <i class="fas fa-money-bill-wave"></i>
                  </div>
                  <h4>Full Payment</h4>
                  <span class="payment-badge">Complete</span>
                </div>
                <p>
                  Complete program cost paid upfront with immediate registration
                  confirmation
                </p>
                <ul>
                  <li>Complete program cost paid upfront</li>
                  <li>Immediate registration confirmation</li>
                  <li>Direct Stripe payment processing</li>
                </ul>
              </div>

              <div class="payment-type-card split-payment">
                <div class="payment-type-header">
                  <div class="payment-icon">
                    <i class="fas fa-piggy-bank"></i>
                  </div>
                  <h4>Split Payment</h4>
                  <span class="payment-badge">Partial</span>
                </div>
                <p>
                  Partial payment with remaining balance tracked for future
                  payment
                </p>
                <ul>
                  <li>Partial payment with remaining balance</li>
                  <li>Payment plan setup</li>
                  <li>Balance tracking and reminders</li>
                </ul>
              </div>

              <div class="payment-type-card recurring-payment">
                <div class="payment-type-header">
                  <div class="payment-icon">
                    <i class="fas fa-sync-alt"></i>
                  </div>
                  <h4>Recurring Payment</h4>
                  <span class="payment-badge">Subscription</span>
                </div>
                <p>
                  Subscription-based payment model with automated monthly
                  billing
                </p>
                <ul>
                  <li>Subscription-based payment model</li>
                  <li>Automated monthly billing</li>
                  <li>Stripe subscription management</li>
                </ul>
              </div>

              <div class="payment-type-card credit-payment">
                <div class="payment-type-header">
                  <div class="payment-icon">
                    <i class="fas fa-gift"></i>
                  </div>
                  <h4>Credit Payment</h4>
                  <span class="payment-badge">Credit</span>
                </div>
                <p>
                  Guardian account credits automatically applied to reduce
                  program costs
                </p>
                <ul>
                  <li>Guardian account credits</li>
                  <li>Automatic credit application</li>
                  <li>Remaining balance calculation</li>
                </ul>
              </div>

              <div class="payment-type-card external-payment">
                <div class="payment-type-header">
                  <div class="payment-icon">
                    <i class="fas fa-share-alt"></i>
                  </div>
                  <h4>External Payment</h4>
                  <span class="payment-badge">Delegated</span>
                </div>
                <p>
                  Delegated payment to other email with secure payment link
                  generation
                </p>
                <ul>
                  <li>Delegated payment to other email</li>
                  <li>Secure payment link generation</li>
                  <li>Payment status tracking</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Coupon System -->
          <div class="coupon-system">
            <h3><i class="fas fa-ticket-alt"></i> Coupon System</h3>

            <div class="coupon-overview">
              <div class="coupon-info">
                <h4>Coupon Creation & Management</h4>
                <div class="coupon-features">
                  <div class="coupon-feature">
                    <i class="fas fa-user-shield"></i>
                    <span>Admin creates coupons for specific programs</span>
                  </div>
                  <div class="coupon-feature">
                    <i class="fas fa-percentage"></i>
                    <span
                      >Coupon types: Percentage or fixed amount discount</span
                    >
                  </div>
                  <div class="coupon-feature">
                    <i class="fas fa-chart-line"></i>
                    <span
                      >Usage limits: Set maximum number of times coupon can be
                      used</span
                    >
                  </div>
                  <div class="coupon-feature">
                    <i class="fas fa-calendar-alt"></i>
                    <span
                      >Validity period: Start and end dates for coupon
                      validity</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <div class="coupon-process">
              <h4>Coupon Application Process</h4>
              <div class="process-steps">
                <div class="process-step">
                  <div class="step-number">1</div>
                  <div class="step-content">
                    <h5>Guardian enters coupon code</h5>
                    <p>During payment process</p>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">2</div>
                  <div class="step-content">
                    <h5>System validates coupon</h5>
                    <p>
                      Checks existence, activity, expiration, and usage limits
                    </p>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">3</div>
                  <div class="step-content">
                    <h5>Discount calculation</h5>
                    <p>Percentage or fixed amount applied</p>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">4</div>
                  <div class="step-content">
                    <h5>Final amount calculation</h5>
                    <p>Original amount minus discount</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="coupon-validation">
              <h4>Coupon Validation Rules</h4>
              <div class="validation-rules">
                <div class="validation-rule">
                  <i class="fas fa-check-circle"></i>
                  <span
                    >Coupon must be associated with the specific program</span
                  >
                </div>
                <div class="validation-rule">
                  <i class="fas fa-check-circle"></i>
                  <span>Coupon must be active (is_active = true)</span>
                </div>
                <div class="validation-rule">
                  <i class="fas fa-check-circle"></i>
                  <span>Current date must be within validity period</span>
                </div>
                <div class="validation-rule">
                  <i class="fas fa-check-circle"></i>
                  <span>Usage count must be less than usage limit</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Processing Components -->
          <div class="payment-components">
            <h3><i class="fas fa-cogs"></i> Payment Processing Components</h3>

            <div class="components-grid">
              <div class="component-card">
                <div class="component-icon">
                  <i class="fas fa-wallet"></i>
                </div>
                <h4>Guardian Credit System</h4>
                <p>
                  Admin can add credits to guardian accounts for automatic
                  application to program costs
                </p>
                <ul>
                  <li>Admin can add credits to guardian accounts</li>
                  <li>Credits automatically applied to reduce program costs</li>
                  <li>Credit usage logged and tracked</li>
                  <li>Remaining balance charged to payment method</li>
                </ul>
              </div>

              <div class="component-card">
                <div class="component-icon">
                  <i class="fab fa-stripe"></i>
                </div>
                <h4>Stripe Integration</h4>
                <p>Seamless payment processing with Stripe API integration</p>
                <ul>
                  <li>Payment intent creation</li>
                  <li>Webhook handling for payment confirmations</li>
                  <li>Subscription management</li>
                  <li>Customer management</li>
                </ul>
              </div>

              <div class="component-card">
                <div class="component-icon">
                  <i class="fas fa-chart-bar"></i>
                </div>
                <h4>Payment Tracking</h4>
                <p>
                  Comprehensive tracking and management of all payment
                  transactions
                </p>
                <ul>
                  <li>Transaction history</li>
                  <li>Payment status monitoring</li>
                  <li>Receipt generation</li>
                  <li>Refund management</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Detailed Payment Workflow -->
          <div class="payment-workflow">
            <h3><i class="fas fa-route"></i> Detailed Payment Workflow</h3>

            <div class="workflow-steps">
              <div class="workflow-step">
                <div class="workflow-icon">
                  <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="workflow-content">
                  <h4>1. Program Selection & Cost Calculation</h4>
                  <p>
                    Guardian selects program → System calculates total cost →
                    Guardian reviews program details
                  </p>
                </div>
              </div>

              <div class="workflow-step">
                <div class="workflow-icon">
                  <i class="fas fa-credit-card"></i>
                </div>
                <div class="workflow-content">
                  <h4>2. Payment Method Selection</h4>
                  <p>
                    Guardian chooses payment type → System shows payment options
                    → Guardian selects method
                  </p>
                </div>
              </div>

              <div class="workflow-step">
                <div class="workflow-icon">
                  <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="workflow-content">
                  <h4>3. Coupon Application (Optional)</h4>
                  <p>
                    Guardian enters coupon code → System validates coupon →
                    Discount applied to total cost
                  </p>
                </div>
              </div>

              <div class="workflow-step">
                <div class="workflow-icon">
                  <i class="fas fa-gift"></i>
                </div>
                <div class="workflow-content">
                  <h4>4. Credit Application (Optional)</h4>
                  <p>
                    System checks available credit → Guardian chooses credit
                    amount → Credit reduces total cost
                  </p>
                </div>
              </div>

              <div class="workflow-step">
                <div class="workflow-icon">
                  <i class="fas fa-calculator"></i>
                </div>
                <div class="workflow-content">
                  <h4>5. Final Amount Calculation</h4>
                  <p>
                    Original cost - Coupon discount - Credit amount = Final
                    amount to pay
                  </p>
                </div>
              </div>

              <div class="workflow-step">
                <div class="workflow-icon">
                  <i class="fas fa-cogs"></i>
                </div>
                <div class="workflow-content">
                  <h4>6. Payment Processing</h4>
                  <p>
                    Guardian enters payment details → Stripe processes payment →
                    Payment confirmation
                  </p>
                </div>
              </div>

              <div class="workflow-step">
                <div class="workflow-icon">
                  <i class="fas fa-check-circle"></i>
                </div>
                <div class="workflow-content">
                  <h4>7. Registration Completion</h4>
                  <p>
                    Payment successful → Player registered for program →
                    Confirmation email sent
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Method Specific Workflows -->
          <div class="payment-methods">
            <h3>
              <i class="fas fa-credit-card"></i> Payment Method Specific
              Workflows
            </h3>

            <div class="method-workflows">
              <div class="method-workflow">
                <div class="method-header">
                  <h4>
                    <i class="fas fa-money-bill-wave"></i>
                    Full Payment Workflow
                  </h4>
                </div>
                <div class="method-steps">
                  <div class="method-step">
                    <span class="step-number">1</span>
                    <span>Guardian selects "Full Payment"</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">2</span>
                    <span>System shows total program cost</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">3</span>
                    <span>Guardian applies coupon/credit if available</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">4</span>
                    <span>Guardian enters payment method details</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">5</span>
                    <span>Stripe processes full payment</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">6</span>
                    <span>Registration completed immediately</span>
                  </div>
                </div>
              </div>

              <div class="method-workflow">
                <div class="method-header">
                  <h4>
                    <i class="fas fa-piggy-bank"></i> Split Payment Workflow
                  </h4>
                </div>
                <div class="method-steps">
                  <div class="method-step">
                    <span class="step-number">1</span>
                    <span>Guardian selects "Split Payment"</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">2</span>
                    <span>Guardian specifies amount to pay now</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">3</span>
                    <span>System calculates remaining balance</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">4</span>
                    <span>Guardian pays initial amount</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">5</span>
                    <span>Remaining balance tracked for future payment</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">6</span>
                    <span>Registration completed with pending balance</span>
                  </div>
                </div>
              </div>

              <div class="method-workflow">
                <div class="method-header">
                  <h4>
                    <i class="fas fa-sync-alt"></i>
                    Recurring Payment Workflow
                  </h4>
                </div>
                <div class="method-steps">
                  <div class="method-step">
                    <span class="step-number">1</span>
                    <span>Guardian selects "Recurring Payment"</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">2</span>
                    <span>Guardian pays down payment</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">3</span>
                    <span>System creates Stripe subscription</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">4</span>
                    <span>Monthly payments automatically charged</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">5</span>
                    <span>Registration completed with subscription active</span>
                  </div>
                </div>
              </div>

              <div class="method-workflow">
                <div class="method-header">
                  <h4><i class="fas fa-gift"></i> Credit Payment Workflow</h4>
                </div>
                <div class="method-steps">
                  <div class="method-step">
                    <span class="step-number">1</span>
                    <span>Guardian selects "Use Credit"</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">2</span>
                    <span>System applies available credit</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">3</span>
                    <span
                      >If credit covers full cost: Registration completed</span
                    >
                  </div>
                  <div class="method-step">
                    <span class="step-number">4</span>
                    <span
                      >If partial credit: Remaining amount charged to payment
                      method</span
                    >
                  </div>
                  <div class="method-step">
                    <span class="step-number">5</span>
                    <span>Credit usage logged and tracked</span>
                  </div>
                </div>
              </div>

              <div class="method-workflow">
                <div class="method-header">
                  <h4>
                    <i class="fas fa-share-alt"></i>
                    External Payment Workflow
                  </h4>
                </div>
                <div class="method-steps">
                  <div class="method-step">
                    <span class="step-number">1</span>
                    <span>Guardian selects "External Payment"</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">2</span>
                    <span>Guardian enters email for payment delegation</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">3</span>
                    <span>System generates secure payment link</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">4</span>
                    <span>Payment link sent to specified email</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">5</span>
                    <span>External payer completes payment</span>
                  </div>
                  <div class="method-step">
                    <span class="step-number">6</span>
                    <span
                      >Registration completed upon payment confirmation</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Invitation System Workflows Section -->
      <section id="invitation-system" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-envelope"></i> Invitation System Workflows</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            The invitation system is a core component of the Mass Premier Courts
            platform, enabling admins and coaches to invite players to programs
            and teams. This section details the complete invitation workflows,
            from creation to response handling.
          </p>

          <!-- Invitation Types Overview -->
          <div class="invitation-overview">
            <h3><i class="fas fa-list-ul"></i> Invitation Types Overview</h3>
            <div class="invitation-types-grid">
              <div class="invitation-type-card admin">
                <div class="invitation-type-header">
                  <div class="invitation-icon">
                    <i class="fas fa-crown"></i>
                  </div>
                  <h4>Admin Invitations</h4>
                  <span class="invitation-badge">System</span>
                </div>
                <p>
                  Program and team invitations sent by system administrators
                </p>
                <ul>
                  <li>Program invitations to players</li>
                  <li>Post-tryout team invitations</li>
                  <li>System-generated notifications</li>
                  <li>Admin-controlled payment amounts</li>
                </ul>
              </div>

              <div class="invitation-type-card coach">
                <div class="invitation-type-header">
                  <div class="invitation-icon">
                    <i class="fas fa-user-graduate"></i>
                  </div>
                  <h4>Coach Invitations</h4>
                  <span class="invitation-badge">Team</span>
                </div>
                <p>Player and coach invitations sent by team coaches</p>
                <ul>
                  <li>Player invitations to teams</li>
                  <li>Coach collaboration invitations</li>
                  <li>Team-specific invitations</li>
                  <li>Coach-set payment amounts</li>
                </ul>
              </div>

              <div class="invitation-type-card guardian">
                <div class="invitation-type-header">
                  <div class="invitation-icon">
                    <i class="fas fa-user-friends"></i>
                  </div>
                  <h4>Guardian Response</h4>
                  <span class="invitation-badge">Response</span>
                </div>
                <p>Guardian response system for invitation management</p>
                <ul>
                  <li>Invitation acceptance/rejection</li>
                  <li>Multi-player family handling</li>
                  <li>Payment processing initiation</li>
                  <li>Response tracking</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Admin Invitations -->
          <div class="invitation-section">
            <h3><i class="fas fa-crown"></i> Admin Invitations</h3>

            <div class="invitation-workflow">
              <div class="workflow-header">
                <h4>Program Invitation Process</h4>
                <p>Complete workflow for admin-sent program invitations</p>
              </div>

              <div class="workflow-steps-detailed">
                <div class="workflow-step-detailed">
                  <div class="step-number-large">1</div>
                  <div class="step-content-detailed">
                    <h5>Admin selects program</h5>
                    <p>Admin chooses program from admin dashboard</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">2</div>
                  <div class="step-content-detailed">
                    <h5>Admin searches for players</h5>
                    <p>
                      Uses filters (age, gender, grade, etc.) to find eligible
                      players
                    </p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">3</div>
                  <div class="step-content-detailed">
                    <h5>Admin selects eligible players</h5>
                    <p>Chooses players for invitation</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">4</div>
                  <div class="step-content-detailed">
                    <h5>System creates invitations</h5>
                    <p>
                      Creates records in admin_invites_player_for_program table
                    </p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">5</div>
                  <div class="step-content-detailed">
                    <h5>Email notifications sent</h5>
                    <p>Guardians of selected players receive invitations</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">6</div>
                  <div class="step-content-detailed">
                    <h5>Guardians receive invitations</h5>
                    <p>Invitations appear in guardian dashboard</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">7</div>
                  <div class="step-content-detailed">
                    <h5>Guardians can accept/reject</h5>
                    <p>Guardians make decision on invitations</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">8</div>
                  <div class="step-content-detailed">
                    <h5>Payment process begins</h5>
                    <p>Upon acceptance, payment processing starts</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="invitation-workflow">
              <div class="workflow-header">
                <h4>Post-Tryout Team Invitation Process</h4>
                <p>Workflow for assigning players to teams after tryouts</p>
              </div>

              <div class="workflow-steps-detailed">
                <div class="workflow-step-detailed">
                  <div class="step-number-large">1</div>
                  <div class="step-content-detailed">
                    <h5>Admin selects tryout program</h5>
                    <p>Chooses tryout program that has ended</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">2</div>
                  <div class="step-content-detailed">
                    <h5>Admin views tryout results</h5>
                    <p>Reviews player performance and tryout data</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">3</div>
                  <div class="step-content-detailed">
                    <h5>Admin assigns players to teams</h5>
                    <p>Based on tryout performance and team needs</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">4</div>
                  <div class="step-content-detailed">
                    <h5>System creates team invitations</h5>
                    <p>With specific payment amounts set by admin</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">5</div>
                  <div class="step-content-detailed">
                    <h5>Guardians receive team invitations</h5>
                    <p>With payment details and team assignment</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">6</div>
                  <div class="step-content-detailed">
                    <h5>Guardians accept/reject</h5>
                    <p>Team assignments and payment processing</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Coach Invitations -->
          <div class="invitation-section">
            <h3><i class="fas fa-user-graduate"></i> Coach Invitations</h3>

            <div class="invitation-workflow">
              <div class="workflow-header">
                <h4>Player Invitation Process</h4>
                <p>Complete workflow for coach-sent player invitations</p>
              </div>

              <div class="workflow-steps-detailed">
                <div class="workflow-step-detailed">
                  <div class="step-number-large">1</div>
                  <div class="step-content-detailed">
                    <h5>Coach logs into dashboard</h5>
                    <p>Accesses team management interface</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">2</div>
                  <div class="step-content-detailed">
                    <h5>Coach searches for available players</h5>
                    <p>Uses search functionality to find players</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">3</div>
                  <div class="step-content-detailed">
                    <h5>Coach selects players to invite</h5>
                    <p>Chooses players for team invitation</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">4</div>
                  <div class="step-content-detailed">
                    <h5>Coach sets payment amount</h5>
                    <p>Specifies payment amount for each player</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">5</div>
                  <div class="step-content-detailed">
                    <h5>Coach enters guardian emails</h5>
                    <p>Provides email addresses for notification</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">6</div>
                  <div class="step-content-detailed">
                    <h5>System validates emails</h5>
                    <p>Confirms emails match player/guardian records</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">7</div>
                  <div class="step-content-detailed">
                    <h5>System creates invitations</h5>
                    <p>Creates records in player_invitations table</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">8</div>
                  <div class="step-content-detailed">
                    <h5>Email notifications sent</h5>
                    <p>Guardians receive invitation emails</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">9</div>
                  <div class="step-content-detailed">
                    <h5>Guardians receive invitations</h5>
                    <p>Invitations appear in guardian dashboard</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">10</div>
                  <div class="step-content-detailed">
                    <h5>Guardians accept/reject</h5>
                    <p>Guardians make decision on team invitations</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">11</div>
                  <div class="step-content-detailed">
                    <h5>Player added to team</h5>
                    <p>
                      Upon acceptance, player joins team and payment process
                      begins
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="invitation-workflow">
              <div class="workflow-header">
                <h4>Coach Collaboration Process</h4>
                <p>Workflow for inviting assistant coaches to teams</p>
              </div>

              <div class="workflow-steps-detailed">
                <div class="workflow-step-detailed">
                  <div class="step-number-large">1</div>
                  <div class="step-content-detailed">
                    <h5>Primary coach searches</h5>
                    <p>Looks for other coaches to invite</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">2</div>
                  <div class="step-content-detailed">
                    <h5>Coach sends invitation</h5>
                    <p>Invites assistant coach to team</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">3</div>
                  <div class="step-content-detailed">
                    <h5>Assistant coach receives invitation</h5>
                    <p>Gets invitation email</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">4</div>
                  <div class="step-content-detailed">
                    <h5>Assistant coach accepts</h5>
                    <p>Accepts team invitation</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">5</div>
                  <div class="step-content-detailed">
                    <h5>System grants team access</h5>
                    <p>Assistant coach gets team access</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">6</div>
                  <div class="step-content-detailed">
                    <h5>Assistant coach can manage team</h5>
                    <p>With limited permissions</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Guardian Response System -->
          <div class="invitation-section">
            <h3>
              <i class="fas fa-user-friends"></i> Guardian Response System
            </h3>

            <div class="invitation-workflow">
              <div class="workflow-header">
                <h4>Invitation Response Process</h4>
                <p>Complete workflow for guardian invitation responses</p>
              </div>

              <div class="workflow-steps-detailed">
                <div class="workflow-step-detailed">
                  <div class="step-number-large">1</div>
                  <div class="step-content-detailed">
                    <h5>Guardian receives notification</h5>
                    <p>Email + dashboard notification</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">2</div>
                  <div class="step-content-detailed">
                    <h5>Guardian reviews invitation details</h5>
                    <p>
                      Program/team information, payment amount, player
                      assignment
                    </p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">3</div>
                  <div class="step-content-detailed">
                    <h5>Guardian makes decision</h5>
                    <p>Accepts or rejects invitation</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">4</div>
                  <div class="step-content-detailed">
                    <h5>If accepted: Payment process begins</h5>
                    <p>System initiates payment workflow</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">5</div>
                  <div class="step-content-detailed">
                    <h5>Guardian completes payment</h5>
                    <p>Processes payment for program/team</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">6</div>
                  <div class="step-content-detailed">
                    <h5>Player registered for program/team</h5>
                    <p>Registration completed successfully</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">7</div>
                  <div class="step-content-detailed">
                    <h5>Confirmation sent to coach/admin</h5>
                    <p>Notification of successful registration</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">8</div>
                  <div class="step-content-detailed">
                    <h5>If rejected: Invitation marked as declined</h5>
                    <p>Notification sent to coach/admin</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="invitation-workflow">
              <div class="workflow-header">
                <h4>Multi-Player Family Handling</h4>
                <p>Workflow for families with multiple eligible players</p>
              </div>

              <div class="workflow-steps-detailed">
                <div class="workflow-step-detailed">
                  <div class="step-number-large">1</div>
                  <div class="step-content-detailed">
                    <h5>Guardian receives invitation</h5>
                    <p>For specific player</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">2</div>
                  <div class="step-content-detailed">
                    <h5>Guardian reviews invitation</h5>
                    <p>And payment details</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">3</div>
                  <div class="step-content-detailed">
                    <h5>Guardian can select different player</h5>
                    <p>If multiple children eligible</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">4</div>
                  <div class="step-content-detailed">
                    <h5>System recalculates payment</h5>
                    <p>Based on selected player</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">5</div>
                  <div class="step-content-detailed">
                    <h5>Guardian completes payment</h5>
                    <p>For selected player</p>
                  </div>
                </div>

                <div class="workflow-step-detailed">
                  <div class="step-number-large">6</div>
                  <div class="step-content-detailed">
                    <h5>Only selected player registered</h5>
                    <p>For program/team</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Invitation Tracking -->
          <div class="invitation-tracking">
            <h3><i class="fas fa-chart-line"></i> Invitation Tracking</h3>

            <div class="tracking-overview">
              <div class="tracking-card">
                <div class="tracking-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <h4>Status Management</h4>
                <div class="status-list">
                  <div class="status-item pending">
                    <span class="status-dot"></span>
                    <span>Pending: Invitation sent, awaiting response</span>
                  </div>
                  <div class="status-item accepted">
                    <span class="status-dot"></span>
                    <span>Accepted: Guardian accepted, payment processing</span>
                  </div>
                  <div class="status-item declined">
                    <span class="status-dot"></span>
                    <span>Declined: Guardian rejected invitation</span>
                  </div>
                  <div class="status-item expired">
                    <span class="status-dot"></span>
                    <span>Expired: Invitation time limit exceeded</span>
                  </div>
                </div>
              </div>

              <div class="tracking-card">
                <div class="tracking-icon">
                  <i class="fas fa-bell"></i>
                </div>
                <h4>Notification System</h4>
                <div class="notification-list">
                  <div class="notification-item">
                    <i class="fas fa-envelope"></i>
                    <span>Email notifications for all invitation events</span>
                  </div>
                  <div class="notification-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard updates for invitation status</span>
                  </div>
                  <div class="notification-item">
                    <i class="fas fa-credit-card"></i>
                    <span>Payment reminders for accepted invitations</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Multi-Role User Management Section -->
      <section id="multi-role" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-users-cog"></i> Multi-Role User Management</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            The Mass Premier Courts platform supports users with multiple roles,
            allowing seamless switching between different user types. This
            section details how the system handles multi-role users, role
            switching, and data isolation between different roles.
          </p>

          <!-- Role Switching Overview -->
          <div class="role-switching-overview">
            <h3><i class="fas fa-exchange-alt"></i> Role Switching System</h3>
            <div class="role-switching-grid">
              <div class="role-switching-card">
                <div class="role-switching-icon">
                  <i class="fas fa-sync-alt"></i>
                </div>
                <h4>Dynamic Role Switching</h4>
                <p>
                  Users can seamlessly switch between different roles within the
                  same account
                </p>
                <ul>
                  <li>Guardian ↔ Coach switching</li>
                  <li>Session-based role management</li>
                  <li>Automatic dashboard redirection</li>
                  <li>Role-specific data isolation</li>
                </ul>
              </div>

              <div class="role-switching-card">
                <div class="role-switching-icon">
                  <i class="fas fa-shield-alt"></i>
                </div>
                <h4>Data Isolation</h4>
                <p>Each role maintains separate data and access permissions</p>
                <ul>
                  <li>Role-based access control</li>
                  <li>Separate data views per role</li>
                  <li>Appropriate data sharing</li>
                  <li>Security and privacy protection</li>
                </ul>
              </div>

              <div class="role-switching-card">
                <div class="role-switching-icon">
                  <i class="fas fa-tachometer-alt"></i>
                </div>
                <h4>Role-Specific Dashboards</h4>
                <p>
                  Each role provides a unique dashboard with relevant features
                </p>
                <ul>
                  <li>Guardian Mode: Family management</li>
                  <li>Coach Mode: Team management</li>
                  <li>Role-appropriate navigation</li>
                  <li>Contextual feature access</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Guardian-Coach Switching -->
          <div class="role-switching-section">
            <h3>
              <i class="fas fa-user-friends"></i> Guardian ↔ Coach Switching
            </h3>

            <div class="switching-workflow">
              <div class="workflow-header">
                <h4>Multi-Role User Workflow</h4>
                <p>
                  Complete workflow for users with both guardian and coach roles
                </p>
              </div>

              <div class="switching-steps">
                <div class="switching-step">
                  <div class="step-icon">
                    <i class="fas fa-sign-in-alt"></i>
                  </div>
                  <div class="step-content">
                    <h5>User Login</h5>
                    <p>User logs in with email and password</p>
                  </div>
                </div>

                <div class="switching-step">
                  <div class="step-icon">
                    <i class="fas fa-user-check"></i>
                  </div>
                  <div class="step-content">
                    <h5>Role Detection</h5>
                    <p>System detects user has multiple roles</p>
                  </div>
                </div>

                <div class="switching-step">
                  <div class="step-icon">
                    <i class="fas fa-home"></i>
                  </div>
                  <div class="step-content">
                    <h5>Current Role Dashboard</h5>
                    <p>User lands on dashboard for current role</p>
                  </div>
                </div>

                <div class="switching-step">
                  <div class="step-icon">
                    <i class="fas fa-exchange-alt"></i>
                  </div>
                  <div class="step-content">
                    <h5>Role Switch Request</h5>
                    <p>User clicks role switcher in navigation</p>
                  </div>
                </div>

                <div class="switching-step">
                  <div class="step-icon">
                    <i class="fas fa-cog"></i>
                  </div>
                  <div class="step-content">
                    <h5>Session Update</h5>
                    <p>System updates session with new role</p>
                  </div>
                </div>

                <div class="switching-step">
                  <div class="step-icon">
                    <i class="fas fa-redirect"></i>
                  </div>
                  <div class="step-content">
                    <h5>Dashboard Redirect</h5>
                    <p>User redirected to new role dashboard</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="role-comparison">
              <h4>Role-Specific Features Comparison</h4>
              <div class="role-features-grid">
                <div class="role-features-card guardian">
                  <div class="role-features-header">
                    <div class="role-icon">
                      <i class="fas fa-user-friends"></i>
                    </div>
                    <h5>Guardian Mode</h5>
                  </div>
                  <div class="features-list">
                    <div class="feature-item">
                      <i class="fas fa-users"></i>
                      <span>Family management</span>
                    </div>
                    <div class="feature-item">
                      <i class="fas fa-child"></i>
                      <span>Player registration</span>
                    </div>
                    <div class="feature-item">
                      <i class="fas fa-credit-card"></i>
                      <span>Payment processing</span>
                    </div>
                    <div class="feature-item">
                      <i class="fas fa-envelope"></i>
                      <span>Invitation management</span>
                    </div>
                    <div class="feature-item">
                      <i class="fas fa-chart-bar"></i>
                      <span>Program tracking</span>
                    </div>
                  </div>
                </div>

                <div class="role-features-card coach">
                  <div class="role-features-header">
                    <div class="role-icon">
                      <i class="fas fa-user-graduate"></i>
                    </div>
                    <h5>Coach Mode</h5>
                  </div>
                  <div class="features-list">
                    <div class="feature-item">
                      <i class="fas fa-users"></i>
                      <span>Team management</span>
                    </div>
                    <div class="feature-item">
                      <i class="fas fa-user-plus"></i>
                      <span>Player invitations</span>
                    </div>
                    <div class="feature-item">
                      <i class="fas fa-handshake"></i>
                      <span>Coach collaboration</span>
                    </div>
                    <div class="feature-item">
                      <i class="fas fa-calendar-alt"></i>
                      <span>Program scheduling</span>
                    </div>
                    <div class="feature-item">
                      <i class="fas fa-chart-line"></i>
                      <span>Team performance</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Isolation -->
          <div class="data-isolation-section">
            <h3>
              <i class="fas fa-database"></i> Data Isolation & Access Control
            </h3>

            <div class="isolation-overview">
              <div class="isolation-card">
                <div class="isolation-icon">
                  <i class="fas fa-lock"></i>
                </div>
                <h4>Role-Based Access Control</h4>
                <p>Each role has specific permissions and data access levels</p>
                <div class="access-levels">
                  <div class="access-level">
                    <span class="level-label">Guardian Access:</span>
                    <span class="level-desc"
                      >Family data, player profiles, payment history</span
                    >
                  </div>
                  <div class="access-level">
                    <span class="level-label">Coach Access:</span>
                    <span class="level-desc"
                      >Team data, player rosters, team programs</span
                    >
                  </div>
                  <div class="access-level">
                    <span class="level-label">Shared Access:</span>
                    <span class="level-desc"
                      >User profile, basic account information</span
                    >
                  </div>
                </div>
              </div>

              <div class="isolation-card">
                <div class="isolation-icon">
                  <i class="fas fa-eye"></i>
                </div>
                <h4>Data Visibility Rules</h4>
                <p>Clear rules for what data is visible in each role</p>
                <div class="visibility-rules">
                  <div class="visibility-rule">
                    <div class="rule-icon">
                      <i class="fas fa-check"></i>
                    </div>
                    <div class="rule-content">
                      <h6>Guardian can see:</h6>
                      <p>
                        Own family data, player profiles, payment history,
                        program enrollments
                      </p>
                    </div>
                  </div>
                  <div class="visibility-rule">
                    <div class="rule-icon">
                      <i class="fas fa-check"></i>
                    </div>
                    <div class="rule-content">
                      <h6>Coach can see:</h6>
                      <p>
                        Team rosters, player information, team programs, coach
                        collaborations
                      </p>
                    </div>
                  </div>
                  <div class="visibility-rule">
                    <div class="rule-icon">
                      <i class="fas fa-times"></i>
                    </div>
                    <div class="rule-content">
                      <h6>Cross-role restrictions:</h6>
                      <p>
                        Guardian cannot see other families' data, coach cannot
                        see other teams' data
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Multi-Role User Workflows -->
          <div class="multi-role-workflows">
            <h3>
              <i class="fas fa-project-diagram"></i> Multi-Role User Workflows
            </h3>

            <div class="workflow-examples">
              <div class="workflow-example">
                <div class="example-header">
                  <h4>Guardian-Coach User Scenario</h4>
                  <p>User managing both family and team responsibilities</p>
                </div>

                <div class="scenario-steps">
                  <div class="scenario-step">
                    <div class="step-number">1</div>
                    <div class="step-details">
                      <h5>Morning: Guardian Mode</h5>
                      <p>
                        Check family dashboard, review player programs, process
                        payments
                      </p>
                    </div>
                  </div>

                  <div class="scenario-step">
                    <div class="step-number">2</div>
                    <div class="step-details">
                      <h5>Role Switch</h5>
                      <p>Switch to coach mode for team management</p>
                    </div>
                  </div>

                  <div class="scenario-step">
                    <div class="step-number">3</div>
                    <div class="step-details">
                      <h5>Afternoon: Coach Mode</h5>
                      <p>
                        Manage team roster, send player invitations, coordinate
                        with assistant coaches
                      </p>
                    </div>
                  </div>

                  <div class="scenario-step">
                    <div class="step-number">4</div>
                    <div class="step-details">
                      <h5>Evening: Back to Guardian</h5>
                      <p>
                        Switch back to review family updates and respond to
                        invitations
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="workflow-example">
                <div class="example-header">
                  <h4>Data Synchronization</h4>
                  <p>How data flows between roles for the same user</p>
                </div>

                <div class="sync-flow">
                  <div class="sync-item">
                    <div class="sync-icon">
                      <i class="fas fa-user"></i>
                    </div>
                    <div class="sync-content">
                      <h5>User Profile</h5>
                      <p>Shared across all roles (name, email, contact info)</p>
                    </div>
                  </div>

                  <div class="sync-item">
                    <div class="sync-icon">
                      <i class="fas fa-users"></i>
                    </div>
                    <div class="sync-content">
                      <h5>Guardian Data</h5>
                      <p>
                        Family information, player profiles, payment history
                      </p>
                    </div>
                  </div>

                  <div class="sync-item">
                    <div class="sync-icon">
                      <i class="fas fa-trophy"></i>
                    </div>
                    <div class="sync-content">
                      <h5>Coach Data</h5>
                      <p>Team information, player rosters, team programs</p>
                    </div>
                  </div>

                  <div class="sync-item">
                    <div class="sync-icon">
                      <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="sync-content">
                      <h5>Security Layer</h5>
                      <p>Role-based access control ensures data isolation</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Implementation Details -->
          <div class="implementation-details">
            <h3><i class="fas fa-code"></i> Implementation Details</h3>

            <div class="implementation-grid">
              <div class="implementation-card">
                <div class="implementation-icon">
                  <i class="fas fa-database"></i>
                </div>
                <h4>Database Structure</h4>
                <div class="implementation-content">
                  <div class="impl-item">
                    <span class="impl-label">Users Table:</span>
                    <span class="impl-desc"
                      >Stores user account information</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Roles Table:</span>
                    <span class="impl-desc"
                      >Defines available roles (admin, guardian, coach,
                      player)</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Role_User Table:</span>
                    <span class="impl-desc"
                      >Many-to-many relationship between users and roles</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Current Role Field:</span>
                    <span class="impl-desc"
                      >Tracks user's currently active role</span
                    >
                  </div>
                </div>
              </div>

              <div class="implementation-card">
                <div class="implementation-icon">
                  <i class="fas fa-cogs"></i>
                </div>
                <h4>Session Management</h4>
                <div class="implementation-content">
                  <div class="impl-item">
                    <span class="impl-label">Role Switching:</span>
                    <span class="impl-desc"
                      >Updates session with new current_role</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Dashboard Redirect:</span>
                    <span class="impl-desc"
                      >Routes user to appropriate dashboard</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Permission Check:</span>
                    <span class="impl-desc"
                      >Validates access based on current role</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Data Filtering:</span>
                    <span class="impl-desc"
                      >Shows only role-appropriate data</span
                    >
                  </div>
                </div>
              </div>

              <div class="implementation-card">
                <div class="implementation-icon">
                  <i class="fas fa-shield-alt"></i>
                </div>
                <h4>Security Considerations</h4>
                <div class="implementation-content">
                  <div class="impl-item">
                    <span class="impl-label">Role Validation:</span>
                    <span class="impl-desc"
                      >Ensures user has permission for requested role</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Data Isolation:</span>
                    <span class="impl-desc"
                      >Prevents cross-role data access</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Session Security:</span>
                    <span class="impl-desc"
                      >Secure role switching without session hijacking</span
                    >
                  </div>
                  <div class="impl-item">
                    <span class="impl-label">Audit Trail:</span>
                    <span class="impl-desc"
                      >Logs role switches for security monitoring</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Program Management Workflows Section -->
      <section id="program-management" class="content-section">
        <div class="section-header">
          <h2>
            <i class="fas fa-calendar-alt"></i> Program Management Workflows
          </h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            Program management is a core function of the Mass Premier Courts
            platform, enabling admins to create, configure, and manage sports
            programs. This section details the complete program lifecycle from
            creation to completion, including eligibility logic and registration
            workflows.
          </p>

          <!-- Program Creation & Configuration -->
          <div class="program-creation-section">
            <h3>
              <i class="fas fa-plus-circle"></i> Program Creation &
              Configuration
            </h3>

            <div class="program-specs-overview">
              <h4>Program Specifications</h4>
              <p>
                When an admin creates a program, they configure the following
                specifications:
              </p>

              <div class="specs-grid">
                <div class="specs-card">
                  <div class="specs-icon">
                    <i class="fas fa-info-circle"></i>
                  </div>
                  <h5>Basic Information</h5>
                  <div class="specs-list">
                    <div class="spec-item">
                      <span class="spec-label"
                        >Program name, description, and location</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Sport type (basketball, volleyball, pickleball)</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Program type (Individual, AAU, Team, Tryout)</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Season (Fall, Winter, Spring, Summer)</span
                      >
                    </div>
                  </div>
                </div>

                <div class="specs-card">
                  <div class="specs-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <h5>Eligibility Criteria</h5>
                  <div class="specs-list">
                    <div class="spec-item">
                      <span class="spec-label"
                        >Age Restrictions: Minimum and maximum age limits</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Gender: Boys, Girls, or Coed</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Grade: Specific grade requirements (optional)</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Birth Date Cutoff: Players must be born on or after
                        this date</span
                      >
                    </div>
                  </div>
                </div>

                <div class="specs-card">
                  <div class="specs-icon">
                    <i class="fas fa-calendar"></i>
                  </div>
                  <h5>Scheduling & Registration</h5>
                  <div class="specs-list">
                    <div class="spec-item">
                      <span class="spec-label">Start and end dates</span>
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Registration opening and closing dates</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Session times and frequency</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Maximum number of registrations</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label">Waitlist enablement</span>
                    </div>
                  </div>
                </div>

                <div class="specs-card">
                  <div class="specs-icon">
                    <i class="fas fa-dollar-sign"></i>
                  </div>
                  <h5>Payment Configuration</h5>
                  <div class="specs-list">
                    <div class="spec-item">
                      <span class="spec-label">Program cost</span>
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Payment types supported (full, split, recurring)</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label">Early bird pricing options</span>
                    </div>
                    <div class="spec-item">
                      <span class="spec-label">Coupon codes</span>
                    </div>
                  </div>
                </div>

                <div class="specs-card">
                  <div class="specs-icon">
                    <i class="fas fa-toggle-on"></i>
                  </div>
                  <h5>Program Status</h5>
                  <div class="specs-list">
                    <div class="spec-item">
                      <span class="spec-label"
                        >Draft (not visible to guardians)</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Public (visible and available for registration)</span
                      >
                    </div>
                    <div class="spec-item">
                      <span class="spec-label"
                        >Archived (no longer available)</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Program Eligibility Logic -->
          <div class="eligibility-section">
            <h3>
              <i class="fas fa-check-circle"></i> Program Eligibility Logic
            </h3>

            <div class="eligibility-overview">
              <h4>Guardian Dashboard Program Display</h4>
              <p>
                Programs are shown to guardians in their dashboard based on the
                following logic:
              </p>

              <div class="eligibility-workflow">
                <div class="workflow-header">
                  <h5>Step 1: Program Filtering</h5>
                  <p>Initial filtering of programs based on system criteria</p>
                </div>

                <div class="filter-criteria">
                  <div class="criteria-item">
                    <div class="criteria-icon">
                      <i class="fas fa-eye"></i>
                    </div>
                    <div class="criteria-content">
                      <h6>Status Check</h6>
                      <p>
                        Only programs with
                        <code>status = 'public'</code>
                        are displayed
                      </p>
                    </div>
                  </div>

                  <div class="criteria-item">
                    <div class="criteria-icon">
                      <i class="fas fa-edit"></i>
                    </div>
                    <div class="criteria-content">
                      <h6>Draft Check</h6>
                      <p>
                        Only programs with
                        <code>is_draft = false</code>
                        are shown
                      </p>
                    </div>
                  </div>

                  <div class="criteria-item">
                    <div class="criteria-icon">
                      <i class="fas fa-clock"></i>
                    </div>
                    <div class="criteria-content">
                      <h6>Registration Period</h6>
                      <p>
                        Only programs with
                        <code>registration_closing_date >= today</code>
                        are available
                      </p>
                    </div>
                  </div>

                  <div class="criteria-item">
                    <div class="criteria-icon">
                      <i class="fas fa-list"></i>
                    </div>
                    <div class="criteria-content">
                      <h6>Program Type</h6>
                      <p>
                        Only programs of type 'Individual' or 'AAU' are shown
                        (Team and Tryout programs require invitations)
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="eligibility-workflow">
                <div class="workflow-header">
                  <h5>Step 2: Player Eligibility Check</h5>
                  <p>
                    For each player in the guardian's family, the system checks
                    eligibility criteria
                  </p>
                </div>

                <div class="eligibility-checks">
                  <div class="check-item">
                    <div class="check-icon">
                      <i class="fas fa-birthday-cake"></i>
                    </div>
                    <div class="check-content">
                      <h6>Age Restriction Validation</h6>
                      <div class="code-block">
                        <pre><code>// If program has age restrictions
if ($program->age_restriction_from && $program->age_restriction_to) {
    $playerAge = Carbon::parse($player->birthDate)->age;
    if ($playerAge < $program->age_restriction_from ||
        $playerAge > $program->age_restriction_to) {
        // Player not eligible
    }
}</code></pre>
                      </div>
                    </div>
                  </div>

                  <div class="check-item">
                    <div class="check-icon">
                      <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="check-content">
                      <h6>Birth Date Cutoff Validation</h6>
                      <div class="code-block">
                        <pre><code>// Player must be born on or after the cutoff date
if ($program->birth_date_cutoff &&
    $player->birthDate < $program->birth_date_cutoff) {
    // Player not eligible
}</code></pre>
                      </div>
                    </div>
                  </div>

                  <div class="check-item">
                    <div class="check-icon">
                      <i class="fas fa-venus-mars"></i>
                    </div>
                    <div class="check-content">
                      <h6>Gender Validation</h6>
                      <div class="code-block">
                        <pre><code>// Gender matching logic
if ($player->gender === 'boy') {
    // Eligible for 'boys' or 'coed' programs
} elseif ($player->gender === 'girl') {
    // Eligible for 'girls' or 'coed' programs
}</code></pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="eligibility-workflow">
                <div class="workflow-header">
                  <h5>Step 3: Program Display</h5>
                  <p>Final program display logic and ordering</p>
                </div>

                <div class="display-logic">
                  <div class="logic-item">
                    <div class="logic-icon">
                      <i class="fas fa-check"></i>
                    </div>
                    <div class="logic-content">
                      <h6>Eligibility Check</h6>
                      <p>
                        Programs are shown if at least one player in the family
                        is eligible
                      </p>
                    </div>
                  </div>

                  <div class="logic-item">
                    <div class="logic-icon">
                      <i class="fas fa-sort"></i>
                    </div>
                    <div class="logic-content">
                      <h6>Ordering</h6>
                      <p>
                        Programs are ordered by creation date (newest first)
                      </p>
                    </div>
                  </div>

                  <div class="logic-item">
                    <div class="logic-icon">
                      <i class="fas fa-list-ol"></i>
                    </div>
                    <div class="logic-content">
                      <h6>Pagination</h6>
                      <p>Pagination is applied (20 programs per page)</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Program Registration Eligibility -->
          <div class="registration-eligibility-section">
            <h3>
              <i class="fas fa-user-plus"></i> Program Registration Eligibility
              Check
            </h3>

            <div class="registration-workflow">
              <div class="workflow-header">
                <h4>Pre-registration Validation</h4>
                <p>
                  When a guardian attempts to register a player for a program
                </p>
              </div>

              <div class="validation-steps">
                <div class="validation-step">
                  <div class="step-icon">
                    <i class="fas fa-toggle-on"></i>
                  </div>
                  <div class="step-content">
                    <h5>1. Program Status Check</h5>
                    <p>Program must be public and not draft</p>
                  </div>
                </div>

                <div class="validation-step">
                  <div class="step-icon">
                    <i class="fas fa-calendar-check"></i>
                  </div>
                  <div class="step-content">
                    <h5>2. Registration Period Check</h5>
                    <p>Current date must be within registration period</p>
                  </div>
                </div>

                <div class="validation-step">
                  <div class="step-icon">
                    <i class="fas fa-user-check"></i>
                  </div>
                  <div class="step-content">
                    <h5>3. Player Eligibility Check</h5>
                    <p>
                      Player must meet all criteria (age, gender, birth date
                      cutoff)
                    </p>
                  </div>
                </div>

                <div class="validation-step">
                  <div class="step-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="step-content">
                    <h5>4. Capacity Check</h5>
                    <p>Program must not be full (unless waitlist is enabled)</p>
                  </div>
                </div>

                <div class="validation-step">
                  <div class="step-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="step-content">
                    <h5>5. Duplicate Registration Check</h5>
                    <p>Player must not already be registered</p>
                  </div>
                </div>
              </div>

              <div class="registration-process">
                <h4>Registration Process</h4>
                <div class="process-steps">
                  <div class="process-step">
                    <div class="step-number">1</div>
                    <div class="step-details">
                      <h5>Guardian selects eligible player(s)</h5>
                    </div>
                  </div>

                  <div class="process-step">
                    <div class="step-number">2</div>
                    <div class="step-details">
                      <h5>System calculates total cost</h5>
                    </div>
                  </div>

                  <div class="process-step">
                    <div class="step-number">3</div>
                    <div class="step-details">
                      <h5>Guardian chooses payment method</h5>
                    </div>
                  </div>

                  <div class="process-step">
                    <div class="step-number">4</div>
                    <div class="step-details">
                      <h5>Payment processing begins</h5>
                    </div>
                  </div>

                  <div class="process-step">
                    <div class="step-number">5</div>
                    <div class="step-details">
                      <h5>Registration confirmed upon successful payment</h5>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Program Types & Characteristics -->
          <div class="program-types-section">
            <h3><i class="fas fa-tags"></i> Program Types & Characteristics</h3>

            <div class="program-types-grid">
              <div class="program-type-card individual">
                <div class="program-type-header">
                  <div class="program-type-icon">
                    <i class="fas fa-user"></i>
                  </div>
                  <h4>Individual Programs</h4>
                  <span class="program-type-badge">Direct</span>
                </div>
                <div class="program-type-details">
                  <div class="detail-item">
                    <span class="detail-label">Registration Method:</span>
                    <span class="detail-value"
                      >Direct player registration by guardians</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Eligibility:</span>
                    <span class="detail-value"
                      >Based on program specifications (age, gender, birth date
                      cutoff)</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Payment:</span>
                    <span class="detail-value"
                      >Individual payment per player</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Display Logic:</span>
                    <span class="detail-value"
                      >Shown in guardian dashboard if player meets
                      criteria</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Workflow:</span>
                    <span class="detail-value"
                      >Guardian → Player Selection → Payment →
                      Registration</span
                    >
                  </div>
                </div>
              </div>

              <div class="program-type-card aau">
                <div class="program-type-header">
                  <div class="program-type-icon">
                    <i class="fas fa-trophy"></i>
                  </div>
                  <h4>AAU Programs</h4>
                  <span class="program-type-badge">Competitive</span>
                </div>
                <div class="program-type-details">
                  <div class="detail-item">
                    <span class="detail-label">Registration Method:</span>
                    <span class="detail-value"
                      >Same as Individual programs</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Eligibility:</span>
                    <span class="detail-value"
                      >Same criteria as Individual programs</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Payment:</span>
                    <span class="detail-value"
                      >Individual payment per player</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Display Logic:</span>
                    <span class="detail-value"
                      >Shown in guardian dashboard if player meets
                      criteria</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Workflow:</span>
                    <span class="detail-value"
                      >Guardian → Player Selection → Payment →
                      Registration</span
                    >
                  </div>
                </div>
              </div>

              <div class="program-type-card tryout">
                <div class="program-type-header">
                  <div class="program-type-icon">
                    <i class="fas fa-search"></i>
                  </div>
                  <h4>Tryout Programs</h4>
                  <span class="program-type-badge">Invitation</span>
                </div>
                <div class="program-type-details">
                  <div class="detail-item">
                    <span class="detail-label">Registration Method:</span>
                    <span class="detail-value">Admin invitation only</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Eligibility:</span>
                    <span class="detail-value"
                      >Admin selects players after tryout completion</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Payment:</span>
                    <span class="detail-value"
                      >Payment amount set by admin during invitation</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Display Logic:</span>
                    <span class="detail-value"
                      >Not shown in guardian dashboard</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Workflow:</span>
                    <span class="detail-value"
                      >Admin → Player Selection → Invitation → Guardian Response
                      → Payment → Registration</span
                    >
                  </div>
                </div>
              </div>

              <div class="program-type-card team">
                <div class="program-type-header">
                  <div class="program-type-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <h4>Team Programs</h4>
                  <span class="program-type-badge">Team</span>
                </div>
                <div class="program-type-details">
                  <div class="detail-item">
                    <span class="detail-label">Registration Method:</span>
                    <span class="detail-value">Coach invitation only</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Eligibility:</span>
                    <span class="detail-value"
                      >Coach selects players for team</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Payment:</span>
                    <span class="detail-value"
                      >Payment amount set by coach during invitation</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Display Logic:</span>
                    <span class="detail-value"
                      >Not shown in guardian dashboard</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Workflow:</span>
                    <span class="detail-value"
                      >Coach → Player Selection → Invitation → Guardian Response
                      → Payment → Registration</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Program Lifecycle Management -->
          <div class="lifecycle-section">
            <h3><i class="fas fa-route"></i> Program Lifecycle Management</h3>

            <div class="lifecycle-phases">
              <div class="phase-card creation">
                <div class="phase-header">
                  <div class="phase-icon">
                    <i class="fas fa-plus"></i>
                  </div>
                  <h4>Creation Phase</h4>
                </div>
                <div class="phase-steps">
                  <div class="phase-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h5>Admin creates program</h5>
                      <p>With all specifications</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h5>System validates</h5>
                      <p>All required fields</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h5>Program saved</h5>
                      <p>As draft initially</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                      <h5>Admin reviews</h5>
                      <p>And publishes program</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="phase-card active">
                <div class="phase-header">
                  <div class="phase-icon">
                    <i class="fas fa-play"></i>
                  </div>
                  <h4>Active Registration Phase</h4>
                </div>
                <div class="phase-steps">
                  <div class="phase-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h5>Program becomes visible</h5>
                      <p>To eligible guardians</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h5>Guardians can register</h5>
                      <p>Eligible players</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h5>System tracks</h5>
                      <p>Registration count</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                      <h5>Waitlist management</h5>
                      <p>If capacity reached</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="phase-card execution">
                <div class="phase-header">
                  <div class="phase-icon">
                    <i class="fas fa-running"></i>
                  </div>
                  <h4>Program Execution Phase</h4>
                </div>
                <div class="phase-steps">
                  <div class="phase-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h5>Registration closes</h5>
                      <p>On specified date</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h5>Program begins</h5>
                      <p>On start date</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h5>Attendance tracking</h5>
                      <p>If implemented</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                      <h5>Payment monitoring</h5>
                      <p>For outstanding balances</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="phase-card completion">
                <div class="phase-header">
                  <div class="phase-icon">
                    <i class="fas fa-flag-checkered"></i>
                  </div>
                  <h4>Completion Phase</h4>
                </div>
                <div class="phase-steps">
                  <div class="phase-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h5>Program ends</h5>
                      <p>On end date</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h5>Final payments</h5>
                      <p>Processed</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h5>Performance data</h5>
                      <p>Collected</p>
                    </div>
                  </div>
                  <div class="phase-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                      <h5>Program archived</h5>
                      <p>For historical records</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Team Management System Section -->
      <section id="team-management" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-users"></i> Team Management System</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            The Team Management System is a comprehensive solution for creating,
            managing, and organizing sports teams within the Mass Premier Courts
            platform. This section details team creation, player management,
            coach assignments, and team-program relationships.
          </p>

          <!-- Team Creation & Management -->
          <div class="team-creation-section">
            <h3>
              <i class="fas fa-plus-circle"></i> Team Creation & Management
            </h3>

            <div class="team-creation-overview">
              <div class="creation-workflow">
                <div class="workflow-header">
                  <h4>Team Creation Process</h4>
                  <p>Complete workflow for creating and managing teams</p>
                </div>

                <div class="creation-steps">
                  <div class="creation-step">
                    <div class="step-icon">
                      <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="step-content">
                      <h5>Coach creates team</h5>
                      <p>Primary coach initiates team creation process</p>
                    </div>
                  </div>

                  <div class="creation-step">
                    <div class="step-icon">
                      <i class="fas fa-edit"></i>
                    </div>
                    <div class="step-content">
                      <h5>Sets team information</h5>
                      <p>Team name, location, sport type, and other details</p>
                    </div>
                  </div>

                  <div class="creation-step">
                    <div class="step-icon">
                      <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div class="step-content">
                      <h5>Assigns team to programs</h5>
                      <p>Links team to specific sports programs</p>
                    </div>
                  </div>

                  <div class="creation-step">
                    <div class="step-icon">
                      <i class="fas fa-users"></i>
                    </div>
                    <div class="step-content">
                      <h5>Manages team roster</h5>
                      <p>Adds and manages players on the team</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="team-info-cards">
                <div class="info-card">
                  <div class="info-icon">
                    <i class="fas fa-info-circle"></i>
                  </div>
                  <h4>Team Information</h4>
                  <div class="info-list">
                    <div class="info-item">
                      <span class="info-label">Team Name:</span>
                      <span class="info-value"
                        >Unique identifier for the team</span
                      >
                    </div>
                    <div class="info-item">
                      <span class="info-label">Location:</span>
                      <span class="info-value"
                        >Practice and game locations</span
                      >
                    </div>
                    <div class="info-item">
                      <span class="info-label">Sport Type:</span>
                      <span class="info-value"
                        >Basketball, volleyball, pickleball</span
                      >
                    </div>
                    <div class="info-item">
                      <span class="info-label">Age Group:</span>
                      <span class="info-value"
                        >Target age range for players</span
                      >
                    </div>
                    <div class="info-item">
                      <span class="info-label">Season:</span>
                      <span class="info-value"
                        >Fall, Winter, Spring, Summer</span
                      >
                    </div>
                  </div>
                </div>

                <div class="info-card">
                  <div class="info-icon">
                    <i class="fas fa-cog"></i>
                  </div>
                  <h4>Team Settings</h4>
                  <div class="info-list">
                    <div class="info-item">
                      <span class="info-label">Roster Size:</span>
                      <span class="info-value">Maximum number of players</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Practice Schedule:</span>
                      <span class="info-value">Regular practice times</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Game Schedule:</span>
                      <span class="info-value"
                        >Competition dates and times</span
                      >
                    </div>
                    <div class="info-item">
                      <span class="info-label">Team Status:</span>
                      <span class="info-value"
                        >Active, inactive, or archived</span
                      >
                    </div>
                    <div class="info-item">
                      <span class="info-label">Communication:</span>
                      <span class="info-value"
                        >Team messaging and notifications</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Player Management -->
          <div class="player-management-section">
            <h3><i class="fas fa-user-friends"></i> Player Management</h3>

            <div class="player-management-overview">
              <div class="management-workflow">
                <div class="workflow-header">
                  <h4>Player Management Workflow</h4>
                  <p>Complete process for managing team players</p>
                </div>

                <div class="management-steps">
                  <div class="management-step">
                    <div class="step-icon">
                      <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="step-content">
                      <h5>Add players to team</h5>
                      <p>Invite players through invitation system</p>
                    </div>
                  </div>

                  <div class="management-step">
                    <div class="step-icon">
                      <i class="fas fa-user-minus"></i>
                    </div>
                    <div class="step-content">
                      <h5>Remove players from team</h5>
                      <p>Remove players when necessary</p>
                    </div>
                  </div>

                  <div class="management-step">
                    <div class="step-icon">
                      <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="step-content">
                      <h5>Track player participation</h5>
                      <p>Monitor attendance and performance</p>
                    </div>
                  </div>

                  <div class="management-step">
                    <div class="step-icon">
                      <i class="fas fa-edit"></i>
                    </div>
                    <div class="step-content">
                      <h5>Manage player information</h5>
                      <p>Update player details and contact info</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="player-roster-features">
                <div class="roster-feature">
                  <div class="feature-icon">
                    <i class="fas fa-list"></i>
                  </div>
                  <h4>Roster Management</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>View complete team roster</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Player contact information</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Player statistics and history</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Roster size limits and management</span>
                    </div>
                  </div>
                </div>

                <div class="roster-feature">
                  <div class="feature-icon">
                    <i class="fas fa-calendar-check"></i>
                  </div>
                  <h4>Attendance Tracking</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Practice attendance records</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Game participation tracking</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Attendance reports and analytics</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Absence notifications</span>
                    </div>
                  </div>
                </div>

                <div class="roster-feature">
                  <div class="feature-icon">
                    <i class="fas fa-chart-bar"></i>
                  </div>
                  <h4>Performance Monitoring</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Player performance metrics</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Skill development tracking</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Progress reports</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Performance analytics</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Coach Assignment -->
          <div class="coach-assignment-section">
            <h3><i class="fas fa-user-graduate"></i> Coach Assignment</h3>

            <div class="coach-assignment-overview">
              <div class="assignment-workflow">
                <div class="workflow-header">
                  <h4>Coach Assignment Process</h4>
                  <p>Complete workflow for assigning coaches to teams</p>
                </div>

                <div class="assignment-steps">
                  <div class="assignment-step">
                    <div class="step-icon">
                      <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="step-content">
                      <h5>Primary coach assignment</h5>
                      <p>Assign main coach to lead the team</p>
                    </div>
                  </div>

                  <div class="assignment-step">
                    <div class="step-icon">
                      <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="step-content">
                      <h5>Assistant coach invitations</h5>
                      <p>Invite additional coaches to support</p>
                    </div>
                  </div>

                  <div class="assignment-step">
                    <div class="step-icon">
                      <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="step-content">
                      <h5>Role-based permissions</h5>
                      <p>Set appropriate access levels</p>
                    </div>
                  </div>

                  <div class="assignment-step">
                    <div class="step-icon">
                      <i class="fas fa-unlock"></i>
                    </div>
                    <div class="step-content">
                      <h5>Team access management</h5>
                      <p>Grant access to team resources</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="coach-roles">
                <div class="role-card primary">
                  <div class="role-icon">
                    <i class="fas fa-crown"></i>
                  </div>
                  <h4>Primary Coach</h4>
                  <div class="role-permissions">
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Full team management access</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Player invitation and removal</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Assistant coach management</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Team settings and configuration</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Financial management</span>
                    </div>
                  </div>
                </div>

                <div class="role-card assistant">
                  <div class="role-icon">
                    <i class="fas fa-hands-helping"></i>
                  </div>
                  <h4>Assistant Coach</h4>
                  <div class="role-permissions">
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>View team roster and information</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Attendance tracking</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Practice and game management</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-times"></i>
                      <span>Player invitation/removal</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-times"></i>
                      <span>Financial management</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Team-Program Relationships -->
          <div class="team-program-section">
            <h3><i class="fas fa-link"></i> Team-Program Relationships</h3>

            <div class="relationship-overview">
              <div class="relationship-workflow">
                <div class="workflow-header">
                  <h4>Program Registration Process</h4>
                  <p>How teams register and participate in programs</p>
                </div>

                <div class="relationship-steps">
                  <div class="relationship-step">
                    <div class="step-icon">
                      <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div class="step-content">
                      <h5>Teams register for programs</h5>
                      <p>Coach registers team for specific programs</p>
                    </div>
                  </div>

                  <div class="relationship-step">
                    <div class="step-icon">
                      <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div class="step-content">
                      <h5>Program requirements checking</h5>
                      <p>System validates team eligibility</p>
                    </div>
                  </div>

                  <div class="relationship-step">
                    <div class="step-icon">
                      <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="step-content">
                      <h5>Payment processing</h5>
                      <p>Team payment for program participation</p>
                    </div>
                  </div>

                  <div class="relationship-step">
                    <div class="step-icon">
                      <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="step-content">
                      <h5>Participation tracking</h5>
                      <p>Monitor team performance and attendance</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="program-requirements">
                <div class="requirement-card">
                  <div class="requirement-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <h4>Team Requirements</h4>
                  <div class="requirement-list">
                    <div class="requirement-item">
                      <i class="fas fa-check"></i>
                      <span>Minimum roster size</span>
                    </div>
                    <div class="requirement-item">
                      <i class="fas fa-check"></i>
                      <span>Age group compliance</span>
                    </div>
                    <div class="requirement-item">
                      <i class="fas fa-check"></i>
                      <span>Skill level requirements</span>
                    </div>
                    <div class="requirement-item">
                      <i class="fas fa-check"></i>
                      <span>Coach certification</span>
                    </div>
                  </div>
                </div>

                <div class="requirement-card">
                  <div class="requirement-icon">
                    <i class="fas fa-calendar-alt"></i>
                  </div>
                  <h4>Program Requirements</h4>
                  <div class="requirement-list">
                    <div class="requirement-item">
                      <i class="fas fa-check"></i>
                      <span>Registration deadlines</span>
                    </div>
                    <div class="requirement-item">
                      <i class="fas fa-check"></i>
                      <span>Payment schedules</span>
                    </div>
                    <div class="requirement-item">
                      <i class="fas fa-check"></i>
                      <span>Practice commitments</span>
                    </div>
                    <div class="requirement-item">
                      <i class="fas fa-check"></i>
                      <span>Competition schedules</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Team Performance -->
          <div class="team-performance-section">
            <h3><i class="fas fa-trophy"></i> Team Performance</h3>

            <div class="performance-overview">
              <div class="performance-metrics">
                <div class="metric-card">
                  <div class="metric-icon">
                    <i class="fas fa-chart-line"></i>
                  </div>
                  <h4>Track team participation</h4>
                  <div class="metric-details">
                    <div class="metric-item">
                      <span class="metric-label">Practice Attendance:</span>
                      <span class="metric-value"
                        >Track player attendance at practices</span
                      >
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">Game Participation:</span>
                      <span class="metric-value"
                        >Monitor game attendance and performance</span
                      >
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">Team Statistics:</span>
                      <span class="metric-value"
                        >Overall team performance metrics</span
                      >
                    </div>
                  </div>
                </div>

                <div class="metric-card">
                  <div class="metric-icon">
                    <i class="fas fa-user-chart"></i>
                  </div>
                  <h4>Monitor player attendance</h4>
                  <div class="metric-details">
                    <div class="metric-item">
                      <span class="metric-label">Individual Records:</span>
                      <span class="metric-value"
                        >Track each player's attendance</span
                      >
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">Attendance Reports:</span>
                      <span class="metric-value"
                        >Generate attendance analytics</span
                      >
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">Trend Analysis:</span>
                      <span class="metric-value"
                        >Identify attendance patterns</span
                      >
                    </div>
                  </div>
                </div>

                <div class="metric-card">
                  <div class="metric-icon">
                    <i class="fas fa-chart-bar"></i>
                  </div>
                  <h4>Performance reporting</h4>
                  <div class="metric-details">
                    <div class="metric-item">
                      <span class="metric-label">Team Reports:</span>
                      <span class="metric-value"
                        >Overall team performance summaries</span
                      >
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">Player Reports:</span>
                      <span class="metric-value"
                        >Individual player performance data</span
                      >
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">Progress Tracking:</span>
                      <span class="metric-value"
                        >Monitor improvement over time</span
                      >
                    </div>
                  </div>
                </div>

                <div class="metric-card">
                  <div class="metric-icon">
                    <i class="fas fa-medal"></i>
                  </div>
                  <h4>Team statistics</h4>
                  <div class="metric-details">
                    <div class="metric-item">
                      <span class="metric-label">Win/Loss Records:</span>
                      <span class="metric-value"
                        >Track competitive performance</span
                      >
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">Skill Metrics:</span>
                      <span class="metric-value"
                        >Measure skill development</span
                      >
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">Team Rankings:</span>
                      <span class="metric-value">Compare with other teams</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Reporting & Analytics Section -->
      <section id="reporting" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-chart-bar"></i> Reporting & Analytics</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            The Reporting & Analytics system provides comprehensive data
            insights and export capabilities for the Mass Premier Courts
            platform. This section details admin reports, user analytics,
            program statistics, financial reporting, and data export
            functionality.
          </p>

          <!-- Admin Reports -->
          <div class="admin-reports-section">
            <h3><i class="fas fa-user-shield"></i> Admin Reports</h3>

            <div class="reports-overview">
              <div class="reports-grid">
                <div class="report-card user-reports">
                  <div class="report-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="report-header">
                    <h4>User Reports</h4>
                    <span class="report-badge">Comprehensive</span>
                  </div>
                  <div class="report-content">
                    <div class="report-metrics">
                      <div class="metric-item">
                        <i class="fas fa-user-friends"></i>
                        <span>Guardian registration statistics</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-child"></i>
                        <span>Player enrollment data</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-user-graduate"></i>
                        <span>Coach activity tracking</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-chart-line"></i>
                        <span>User growth metrics</span>
                      </div>
                    </div>
                    <div class="report-features">
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Registration trends analysis</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>User engagement metrics</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Role distribution statistics</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="report-card program-reports">
                  <div class="report-icon">
                    <i class="fas fa-calendar-alt"></i>
                  </div>
                  <div class="report-header">
                    <h4>Program Reports</h4>
                    <span class="report-badge">Performance</span>
                  </div>
                  <div class="report-content">
                    <div class="report-metrics">
                      <div class="metric-item">
                        <i class="fas fa-users"></i>
                        <span>Program enrollment statistics</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Revenue tracking</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-percentage"></i>
                        <span>Participation rates</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-trophy"></i>
                        <span>Program performance metrics</span>
                      </div>
                    </div>
                    <div class="report-features">
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Capacity utilization analysis</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Program popularity trends</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Seasonal performance comparison</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="report-card financial-reports">
                  <div class="report-icon">
                    <i class="fas fa-coins"></i>
                  </div>
                  <div class="report-header">
                    <h4>Financial Reports</h4>
                    <span class="report-badge">Revenue</span>
                  </div>
                  <div class="report-content">
                    <div class="report-metrics">
                      <div class="metric-item">
                        <i class="fas fa-credit-card"></i>
                        <span>Payment processing statistics</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-chart-line"></i>
                        <span>Revenue analysis</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-gift"></i>
                        <span>Credit usage tracking</span>
                      </div>
                      <div class="metric-item">
                        <i class="fas fa-undo"></i>
                        <span>Refund management</span>
                      </div>
                    </div>
                    <div class="report-features">
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Revenue trend analysis</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Payment method distribution</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Financial forecasting</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Export Capabilities -->
          <div class="data-export-section">
            <h3><i class="fas fa-download"></i> Data Export Capabilities</h3>

            <div class="export-overview">
              <div class="export-workflow">
                <div class="workflow-header">
                  <h4>Export Process Workflow</h4>
                  <p>
                    Complete process for exporting data and generating reports
                  </p>
                </div>

                <div class="export-steps">
                  <div class="export-step">
                    <div class="step-icon">
                      <i class="fas fa-filter"></i>
                    </div>
                    <div class="step-content">
                      <h5>Select Data Type</h5>
                      <p>
                        Choose from user data, program data, or financial data
                      </p>
                    </div>
                  </div>

                  <div class="export-step">
                    <div class="step-icon">
                      <i class="fas fa-cog"></i>
                    </div>
                    <div class="step-content">
                      <h5>Configure Filters</h5>
                      <p>Set date ranges, program types, and other criteria</p>
                    </div>
                  </div>

                  <div class="export-step">
                    <div class="step-icon">
                      <i class="fas fa-file-excel"></i>
                    </div>
                    <div class="step-content">
                      <h5>Generate Report</h5>
                      <p>Create Excel file with selected data</p>
                    </div>
                  </div>

                  <div class="export-step">
                    <div class="step-icon">
                      <i class="fas fa-download"></i>
                    </div>
                    <div class="step-content">
                      <h5>Download File</h5>
                      <p>Download the generated report file</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="export-types">
                <div class="export-type-card">
                  <div class="export-type-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <h4>User Data Export</h4>
                  <div class="export-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Guardian registration data</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Player enrollment information</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Coach activity records</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>User role distribution</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Contact information</span>
                    </div>
                  </div>
                </div>

                <div class="export-type-card">
                  <div class="export-type-icon">
                    <i class="fas fa-calendar-check"></i>
                  </div>
                  <h4>Program Data Export</h4>
                  <div class="export-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Program enrollment statistics</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Attendance records</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Performance metrics</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Program schedules</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Capacity utilization</span>
                    </div>
                  </div>
                </div>

                <div class="export-type-card">
                  <div class="export-type-icon">
                    <i class="fas fa-chart-pie"></i>
                  </div>
                  <h4>Financial Data Export</h4>
                  <div class="export-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Payment transaction records</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Revenue analysis</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Credit usage reports</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Refund tracking</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Payment method statistics</span>
                    </div>
                  </div>
                </div>

                <div class="export-type-card">
                  <div class="export-type-icon">
                    <i class="fas fa-cogs"></i>
                  </div>
                  <h4>Custom Report Generation</h4>
                  <div class="export-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Custom date ranges</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Program type filtering</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>User role filtering</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Geographic filtering</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Combined data sets</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Report Filtering -->
          <div class="report-filtering-section">
            <h3><i class="fas fa-filter"></i> Report Filtering</h3>

            <div class="filtering-overview">
              <div class="filter-categories">
                <div class="filter-category">
                  <div class="filter-icon">
                    <i class="fas fa-calendar"></i>
                  </div>
                  <h4>Date Range Filtering</h4>
                  <div class="filter-options">
                    <div class="filter-option">
                      <i class="fas fa-calendar-day"></i>
                      <span>Specific date ranges</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-calendar-week"></i>
                      <span>Weekly periods</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-calendar-alt"></i>
                      <span>Monthly periods</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-calendar-check"></i>
                      <span>Seasonal periods</span>
                    </div>
                  </div>
                </div>

                <div class="filter-category">
                  <div class="filter-icon">
                    <i class="fas fa-tags"></i>
                  </div>
                  <h4>Program Type Filtering</h4>
                  <div class="filter-options">
                    <div class="filter-option">
                      <i class="fas fa-user"></i>
                      <span>Individual programs</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-users"></i>
                      <span>Team programs</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-trophy"></i>
                      <span>AAU programs</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-search"></i>
                      <span>Tryout programs</span>
                    </div>
                  </div>
                </div>

                <div class="filter-category">
                  <div class="filter-icon">
                    <i class="fas fa-user-tag"></i>
                  </div>
                  <h4>User Role Filtering</h4>
                  <div class="filter-options">
                    <div class="filter-option">
                      <i class="fas fa-user-shield"></i>
                      <span>Admin users</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-user-friends"></i>
                      <span>Guardian users</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-user-graduate"></i>
                      <span>Coach users</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-child"></i>
                      <span>Player records</span>
                    </div>
                  </div>
                </div>

                <div class="filter-category">
                  <div class="filter-icon">
                    <i class="fas fa-map-marker-alt"></i>
                  </div>
                  <h4>Geographic Filtering</h4>
                  <div class="filter-options">
                    <div class="filter-option">
                      <i class="fas fa-city"></i>
                      <span>City-based filtering</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-map"></i>
                      <span>Regional filtering</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-location-arrow"></i>
                      <span>Distance-based filtering</span>
                    </div>
                    <div class="filter-option">
                      <i class="fas fa-globe"></i>
                      <span>Location clustering</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Analytics Dashboard -->
          <div class="analytics-dashboard-section">
            <h3><i class="fas fa-tachometer-alt"></i> Analytics Dashboard</h3>

            <div class="dashboard-overview">
              <div class="dashboard-metrics">
                <div class="metric-card key-metrics">
                  <div class="metric-icon">
                    <i class="fas fa-chart-line"></i>
                  </div>
                  <h4>Key Performance Indicators</h4>
                  <div class="metric-details">
                    <div class="kpi-item">
                      <span class="kpi-label">Total Users:</span>
                      <span class="kpi-value">Real-time user count</span>
                    </div>
                    <div class="kpi-item">
                      <span class="kpi-label">Active Programs:</span>
                      <span class="kpi-value">Current program count</span>
                    </div>
                    <div class="kpi-item">
                      <span class="kpi-label">Revenue:</span>
                      <span class="kpi-value">Monthly revenue tracking</span>
                    </div>
                    <div class="kpi-item">
                      <span class="kpi-label">Enrollment Rate:</span>
                      <span class="kpi-value"
                        >Program enrollment percentage</span
                      >
                    </div>
                  </div>
                </div>

                <div class="metric-card trend-analysis">
                  <div class="metric-icon">
                    <i class="fas fa-chart-area"></i>
                  </div>
                  <h4>Trend Analysis</h4>
                  <div class="metric-details">
                    <div class="trend-item">
                      <span class="trend-label">User Growth:</span>
                      <span class="trend-value">Monthly growth trends</span>
                    </div>
                    <div class="trend-item">
                      <span class="trend-label">Program Popularity:</span>
                      <span class="trend-value">Most popular programs</span>
                    </div>
                    <div class="trend-item">
                      <span class="trend-label">Revenue Trends:</span>
                      <span class="trend-value">Revenue growth patterns</span>
                    </div>
                    <div class="trend-item">
                      <span class="trend-label">Seasonal Patterns:</span>
                      <span class="trend-value"
                        >Seasonal enrollment trends</span
                      >
                    </div>
                  </div>
                </div>

                <div class="metric-card data-visualization">
                  <div class="metric-icon">
                    <i class="fas fa-chart-pie"></i>
                  </div>
                  <h4>Data Visualization</h4>
                  <div class="metric-details">
                    <div class="visualization-item">
                      <span class="viz-label">User Distribution:</span>
                      <span class="viz-value">Role-based pie charts</span>
                    </div>
                    <div class="visualization-item">
                      <span class="viz-label">Program Types:</span>
                      <span class="viz-value">Program type breakdown</span>
                    </div>
                    <div class="visualization-item">
                      <span class="viz-label">Payment Methods:</span>
                      <span class="viz-value">Payment method distribution</span>
                    </div>
                    <div class="visualization-item">
                      <span class="viz-label">Geographic Spread:</span>
                      <span class="viz-value">Location-based heat maps</span>
                    </div>
                  </div>
                </div>

                <div class="metric-card real-time-monitoring">
                  <div class="metric-icon">
                    <i class="fas fa-eye"></i>
                  </div>
                  <h4>Real-Time Monitoring</h4>
                  <div class="metric-details">
                    <div class="monitoring-item">
                      <span class="monitor-label">Live Registrations:</span>
                      <span class="monitor-value"
                        >Real-time enrollment tracking</span
                      >
                    </div>
                    <div class="monitoring-item">
                      <span class="monitor-label">Payment Processing:</span>
                      <span class="monitor-value">Live payment status</span>
                    </div>
                    <div class="monitoring-item">
                      <span class="monitor-label">System Activity:</span>
                      <span class="monitor-value"
                        >User activity monitoring</span
                      >
                    </div>
                    <div class="monitoring-item">
                      <span class="monitor-label">Performance Alerts:</span>
                      <span class="monitor-value"
                        >System performance notifications</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Security & Access Control Section -->
      <section id="security" class="content-section">
        <div class="section-header">
          <h2><i class="fas fa-shield-alt"></i> Security & Access Control</h2>
          <div class="section-nav">
            <button class="btn btn-primary" onclick="scrollToTop()">
              <i class="fas fa-arrow-up"></i> Back to Top
            </button>
          </div>
        </div>

        <div class="content-card">
          <p class="lead">
            The Security & Access Control system ensures the protection of user
            data, secure authentication, and proper authorization throughout the
            Mass Premier Courts platform. This section details authentication
            systems, authorization mechanisms, security features, and data
            protection measures.
          </p>

          <!-- Authentication System -->
          <div class="authentication-section">
            <h3><i class="fas fa-key"></i> Authentication System</h3>

            <div class="auth-overview">
              <div class="auth-workflow">
                <div class="workflow-header">
                  <h4>Multi-Factor Security Process</h4>
                  <p>
                    Complete authentication workflow with multiple security
                    layers
                  </p>
                </div>

                <div class="auth-steps">
                  <div class="auth-step">
                    <div class="step-icon">
                      <i class="fas fa-envelope"></i>
                    </div>
                    <div class="step-content">
                      <h5>Email/Password Authentication</h5>
                      <p>Primary login credentials validation</p>
                    </div>
                  </div>

                  <div class="auth-step">
                    <div class="step-icon">
                      <i class="fas fa-clock"></i>
                    </div>
                    <div class="step-content">
                      <h5>Session Management</h5>
                      <p>Secure session handling and validation</p>
                    </div>
                  </div>

                  <div class="auth-step">
                    <div class="step-icon">
                      <i class="fas fa-user-tag"></i>
                    </div>
                    <div class="step-content">
                      <h5>Role-Based Access Control</h5>
                      <p>Role-specific permissions and access</p>
                    </div>
                  </div>

                  <div class="auth-step">
                    <div class="step-icon">
                      <i class="fas fa-lock"></i>
                    </div>
                    <div class="step-content">
                      <h5>Secure Password Handling</h5>
                      <p>Encrypted password storage and validation</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="auth-features">
                <div class="auth-feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                  </div>
                  <h4>Security Layers</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Multi-factor authentication</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Password strength requirements</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Account lockout protection</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Brute force protection</span>
                    </div>
                  </div>
                </div>

                <div class="auth-feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <h4>Session Management</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Secure session handling</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Session timeout</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Role-based session data</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Secure logout process</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Authorization System -->
          <div class="authorization-section">
            <h3><i class="fas fa-user-shield"></i> Authorization System</h3>

            <div class="authorization-overview">
              <div class="auth-roles">
                <div class="role-card admin-role">
                  <div class="role-icon">
                    <i class="fas fa-crown"></i>
                  </div>
                  <h4>Admin Permissions</h4>
                  <div class="permission-list">
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Full system access</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>User management</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Program creation</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Financial oversight</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>System configuration</span>
                    </div>
                  </div>
                </div>

                <div class="role-card guardian-role">
                  <div class="role-icon">
                    <i class="fas fa-user-friends"></i>
                  </div>
                  <h4>Guardian Permissions</h4>
                  <div class="permission-list">
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Family management</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Player registration</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Payment processing</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Invitation responses</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-times"></i>
                      <span>Program creation</span>
                    </div>
                  </div>
                </div>

                <div class="role-card coach-role">
                  <div class="role-icon">
                    <i class="fas fa-user-graduate"></i>
                  </div>
                  <h4>Coach Permissions</h4>
                  <div class="permission-list">
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Team management</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Player invitations</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Program participation</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-check"></i>
                      <span>Team settings</span>
                    </div>
                    <div class="permission-item">
                      <i class="fas fa-times"></i>
                      <span>Financial management</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="access-control">
                <div class="control-card">
                  <div class="control-icon">
                    <i class="fas fa-shield-alt"></i>
                  </div>
                  <h4>Role-Based Access Control</h4>
                  <div class="control-details">
                    <div class="control-item">
                      <span class="control-label"
                        >Feature-Level Permissions:</span
                      >
                      <span class="control-value"
                        >Granular access control for each feature</span
                      >
                    </div>
                    <div class="control-item">
                      <span class="control-label"
                        >Data Access Restrictions:</span
                      >
                      <span class="control-value"
                        >Role-specific data visibility</span
                      >
                    </div>
                    <div class="control-item">
                      <span class="control-label"
                        >Action-Based Authorization:</span
                      >
                      <span class="control-value"
                        >Permission-based action validation</span
                      >
                    </div>
                  </div>
                </div>

                <div class="control-card">
                  <div class="control-icon">
                    <i class="fas fa-database"></i>
                  </div>
                  <h4>Data Protection</h4>
                  <div class="control-details">
                    <div class="control-item">
                      <span class="control-label">User Data Encryption:</span>
                      <span class="control-value"
                        >Encrypted storage of sensitive information</span
                      >
                    </div>
                    <div class="control-item">
                      <span class="control-label">Payment Data Security:</span>
                      <span class="control-value"
                        >PCI-compliant payment processing</span
                      >
                    </div>
                    <div class="control-item">
                      <span class="control-label"
                        >Personal Information Protection:</span
                      >
                      <span class="control-value"
                        >GDPR-compliant data handling</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Security Features -->
          <div class="security-features-section">
            <h3><i class="fas fa-lock"></i> Security Features</h3>

            <div class="security-overview">
              <div class="security-workflow">
                <div class="workflow-header">
                  <h4>Security Implementation</h4>
                  <p>Comprehensive security measures and protection systems</p>
                </div>

                <div class="security-steps">
                  <div class="security-step">
                    <div class="step-icon">
                      <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="step-content">
                      <h5>CSRF Protection</h5>
                      <p>Cross-site request forgery prevention</p>
                    </div>
                  </div>

                  <div class="security-step">
                    <div class="step-icon">
                      <i class="fas fa-check-double"></i>
                    </div>
                    <div class="step-content">
                      <h5>Input Validation</h5>
                      <p>Data sanitization and validation</p>
                    </div>
                  </div>

                  <div class="security-step">
                    <div class="step-icon">
                      <i class="fas fa-database"></i>
                    </div>
                    <div class="step-content">
                      <h5>SQL Injection Prevention</h5>
                      <p>Parameterized queries and prepared statements</p>
                    </div>
                  </div>

                  <div class="security-step">
                    <div class="step-icon">
                      <i class="fas fa-code"></i>
                    </div>
                    <div class="step-content">
                      <h5>XSS Protection</h5>
                      <p>Cross-site scripting prevention</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="security-measures">
                <div class="measure-card">
                  <div class="measure-icon">
                    <i class="fas fa-shield-alt"></i>
                  </div>
                  <h4>CSRF Protection</h4>
                  <div class="measure-details">
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Cross-site request forgery protection</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Secure form handling</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Token validation</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Request verification</span>
                    </div>
                  </div>
                </div>

                <div class="measure-card">
                  <div class="measure-icon">
                    <i class="fas fa-filter"></i>
                  </div>
                  <h4>Input Validation</h4>
                  <div class="measure-details">
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Data sanitization</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Input validation</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>SQL injection prevention</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>XSS protection</span>
                    </div>
                  </div>
                </div>

                <div class="measure-card">
                  <div class="measure-icon">
                    <i class="fas fa-database"></i>
                  </div>
                  <h4>Database Security</h4>
                  <div class="measure-details">
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Parameterized queries</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Prepared statements</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Input sanitization</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Query validation</span>
                    </div>
                  </div>
                </div>

                <div class="measure-card">
                  <div class="measure-icon">
                    <i class="fas fa-code"></i>
                  </div>
                  <h4>Code Security</h4>
                  <div class="measure-details">
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>XSS prevention</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Output encoding</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Content Security Policy</span>
                    </div>
                    <div class="measure-item">
                      <i class="fas fa-check"></i>
                      <span>Secure coding practices</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Protection -->
          <div class="data-protection-section">
            <h3><i class="fas fa-user-secret"></i> Data Protection</h3>

            <div class="protection-overview">
              <div class="protection-workflow">
                <div class="workflow-header">
                  <h4>Data Security Measures</h4>
                  <p>Comprehensive data protection and privacy compliance</p>
                </div>

                <div class="protection-steps">
                  <div class="protection-step">
                    <div class="step-icon">
                      <i class="fas fa-lock"></i>
                    </div>
                    <div class="step-content">
                      <h5>Data Encryption</h5>
                      <p>Encryption at rest and in transit</p>
                    </div>
                  </div>

                  <div class="protection-step">
                    <div class="step-icon">
                      <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="step-content">
                      <h5>Privacy Compliance</h5>
                      <p>GDPR and privacy regulation compliance</p>
                    </div>
                  </div>

                  <div class="protection-step">
                    <div class="step-icon">
                      <i class="fas fa-eye-slash"></i>
                    </div>
                    <div class="step-content">
                      <h5>Data Minimization</h5>
                      <p>Collect only necessary data</p>
                    </div>
                  </div>

                  <div class="protection-step">
                    <div class="step-icon">
                      <i class="fas fa-trash"></i>
                    </div>
                    <div class="step-content">
                      <h5>Data Retention</h5>
                      <p>Automatic data cleanup and retention</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="protection-features">
                <div class="protection-feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-lock"></i>
                  </div>
                  <h4>Encryption Standards</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>AES-256 encryption</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>TLS 1.3 for data in transit</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Encrypted database storage</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Secure key management</span>
                    </div>
                  </div>
                </div>

                <div class="protection-feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-gavel"></i>
                  </div>
                  <h4>Privacy Compliance</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>GDPR compliance</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Data subject rights</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Privacy policy enforcement</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Consent management</span>
                    </div>
                  </div>
                </div>

                <div class="protection-feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-eye-slash"></i>
                  </div>
                  <h4>Data Minimization</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Minimal data collection</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Purpose limitation</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Data anonymization</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Access controls</span>
                    </div>
                  </div>
                </div>

                <div class="protection-feature-card">
                  <div class="feature-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <h4>Data Retention</h4>
                  <div class="feature-details">
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Automatic data cleanup</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Retention policies</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Data archiving</span>
                    </div>
                    <div class="detail-item">
                      <i class="fas fa-check"></i>
                      <span>Secure deletion</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <script src="documentation-script.js"></script>
  </body>
</html>
