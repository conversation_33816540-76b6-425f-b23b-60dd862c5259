// Documentation Website JavaScript
document.addEventListener("DOMContentLoaded", function () {
  // Initialize all functionality
  initializeNavigation();
  initializeSidebar();
  initializeMobileMenu();
  initializePDFExport();
  initializeSmoothScrolling();
  initializeActiveSectionTracking();
  initializeAnimations();
});

// Navigation functionality
function initializeNavigation() {
  const navLinks = document.querySelectorAll(".nav-link");
  const sections = document.querySelectorAll(".content-section");

  // Handle navigation clicks
  navLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();

      // Remove active class from all links
      navLinks.forEach((l) => l.classList.remove("active"));

      // Add active class to clicked link
      this.classList.add("active");

      // Get target section
      const targetId = this.getAttribute("href").substring(1);
      const targetSection = document.getElementById(targetId);

      if (targetSection) {
        // Smooth scroll to target
        targetSection.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });

        // Update URL without page reload
        history.pushState(null, null, `#${targetId}`);
      }
    });
  });
}

// Sidebar functionality
function initializeSidebar() {
  const sidebar = document.getElementById("sidebar");
  const sidebarToggle = document.getElementById("sidebarToggle");
  const mainContent = document.getElementById("mainContent");

  // Toggle sidebar on mobile
  if (sidebarToggle) {
    sidebarToggle.addEventListener("click", function () {
      sidebar.classList.toggle("active");
      this.classList.toggle("active");

      // Update toggle icon
      const icon = this.querySelector("i");
      if (sidebar.classList.contains("active")) {
        icon.className = "fas fa-times";
      } else {
        icon.className = "fas fa-bars";
      }
    });
  }

  // Close sidebar when clicking outside on mobile
  document.addEventListener("click", function (e) {
    if (window.innerWidth <= 1024) {
      if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
        sidebar.classList.remove("active");
        if (sidebarToggle) {
          sidebarToggle.classList.remove("active");
          const icon = sidebarToggle.querySelector("i");
          icon.className = "fas fa-bars";
        }
      }
    }
  });

  // Handle window resize
  window.addEventListener("resize", function () {
    if (window.innerWidth > 1024) {
      sidebar.classList.remove("active");
      if (sidebarToggle) {
        sidebarToggle.classList.remove("active");
        const icon = sidebarToggle.querySelector("i");
        icon.className = "fas fa-bars";
      }
    }
  });
}

// Mobile menu functionality
function initializeMobileMenu() {
  const mobileMenuToggle = document.getElementById("mobileMenuToggle");
  const sidebar = document.getElementById("sidebar");

  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener("click", function () {
      sidebar.classList.toggle("active");

      // Update toggle icon
      const icon = this.querySelector("i");
      if (sidebar.classList.contains("active")) {
        icon.className = "fas fa-times";
      } else {
        icon.className = "fas fa-bars";
      }
    });
  }
}

// PDF Export functionality
function initializePDFExport() {
  const pdfExportBtn = document.getElementById("pdfExportBtn");

  if (pdfExportBtn) {
    pdfExportBtn.addEventListener("click", function () {
      // Show loading state
      const originalText = this.innerHTML;
      this.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
      this.disabled = true;

      // Hide sidebar and controls for PDF
      const sidebar = document.getElementById("sidebar");
      const headerControls = document.querySelector(".header-controls");
      const sectionNavs = document.querySelectorAll(".section-nav");

      // Store original display values
      const originalSidebarDisplay = sidebar.style.display;
      const originalHeaderControlsDisplay = headerControls.style.display;
      const originalSectionNavDisplays = Array.from(sectionNavs).map(
        (nav) => nav.style.display
      );

      // Hide elements for PDF
      sidebar.style.display = "none";
      headerControls.style.display = "none";
      sectionNavs.forEach((nav) => (nav.style.display = "none"));

      // Adjust main content for PDF
      const mainContent = document.getElementById("mainContent");
      const originalMarginLeft = mainContent.style.marginLeft;
      mainContent.style.marginLeft = "0";
      mainContent.style.width = "100%";

      // Add PDF-specific class for styling
      document.body.classList.add("pdf-export");

      // Configure PDF options
      const opt = {
        margin: [0.5, 0.5, 0.5, 0.5],
        filename: "Mass_Premier_Courts_Documentation.pdf",
        image: { type: "jpeg", quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          letterRendering: true,
          allowTaint: false,
        },
        jsPDF: {
          unit: "in",
          format: "a4",
          orientation: "portrait",
        },
      };

      // Generate and download PDF
      html2pdf()
        .set(opt)
        .from(mainContent)
        .save()
        .then(() => {
          // Restore original state after PDF generation
          sidebar.style.display = originalSidebarDisplay;
          headerControls.style.display = originalHeaderControlsDisplay;
          sectionNavs.forEach((nav, index) => {
            nav.style.display = originalSectionNavDisplays[index];
          });
          mainContent.style.marginLeft = originalMarginLeft;
          mainContent.style.width = "";

          // Remove PDF-specific class
          document.body.classList.remove("pdf-export");

          // Restore button state
          this.innerHTML = originalText;
          this.disabled = false;
        })
        .catch((error) => {
          console.error("PDF generation failed:", error);

          // Restore original state on error
          sidebar.style.display = originalSidebarDisplay;
          headerControls.style.display = originalHeaderControlsDisplay;
          sectionNavs.forEach((nav, index) => {
            nav.style.display = originalSectionNavDisplays[index];
          });
          mainContent.style.marginLeft = originalMarginLeft;
          mainContent.style.width = "";

          // Remove PDF-specific class
          document.body.classList.remove("pdf-export");

          // Restore button state and show error
          this.innerHTML =
            '<i class="fas fa-exclamation-triangle"></i> PDF Generation Failed';
          setTimeout(() => {
            this.innerHTML = originalText;
            this.disabled = false;
          }, 3000);
        });
    });
  }
}

// Smooth scrolling functionality
function initializeSmoothScrolling() {
  // Back to top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }

  // Make scrollToTop function globally available
  window.scrollToTop = scrollToTop;

  // Add scroll to top button functionality
  const scrollToTopButtons = document.querySelectorAll(
    '.btn[onclick="scrollToTop()"]'
  );
  scrollToTopButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();
      scrollToTop();
    });
  });
}

// Active section tracking
function initializeActiveSectionTracking() {
  const sections = document.querySelectorAll(".content-section");
  const navLinks = document.querySelectorAll(".nav-link");

  // Intersection Observer for tracking active sections
  const observerOptions = {
    root: null,
    rootMargin: "-20% 0px -70% 0px",
    threshold: 0,
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const currentId = entry.target.id;

        // Update navigation
        navLinks.forEach((link) => {
          link.classList.remove("active");
          if (link.getAttribute("href") === `#${currentId}`) {
            link.classList.add("active");
          }
        });

        // Update URL
        history.replaceState(null, null, `#${currentId}`);
      }
    });
  }, observerOptions);

  // Observe all sections
  sections.forEach((section) => {
    observer.observe(section);
  });

  // Handle initial page load with hash
  if (window.location.hash) {
    const targetSection = document.querySelector(window.location.hash);
    if (targetSection) {
      // Update active navigation
      navLinks.forEach((link) => {
        link.classList.remove("active");
        if (link.getAttribute("href") === window.location.hash) {
          link.classList.add("active");
        }
      });

      // Scroll to section
      setTimeout(() => {
        targetSection.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  }
}

// Animation functionality
function initializeAnimations() {
  // Intersection Observer for animations
  const animationObserver = new IntersectionObserver(
    function (entries) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = "1";
          entry.target.style.transform = "translateY(0)";
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    }
  );

  // Observe elements for animation (excluding content-card to prevent hiding)
  const animatedElements = document.querySelectorAll(
    ".tech-item, .feature-card, .report-card, .export-type-card, .filter-category, .metric-card, .auth-feature-card, .role-card, .control-card, .measure-card, .protection-feature-card"
  );
  animatedElements.forEach((el) => {
    el.style.opacity = "0";
    el.style.transform = "translateY(30px)";
    el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
    animationObserver.observe(el);
  });

  // Ensure all content cards are always visible
  const contentCards = document.querySelectorAll(".content-card");
  contentCards.forEach((card) => {
    card.style.opacity = "1";
    card.style.transform = "translateY(0)";
    card.style.transition = "opacity 0.6s ease, transform 0.6s ease";
  });
}

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Search functionality (for future implementation)
function initializeSearch() {
  // This can be expanded later to add search functionality
  console.log("Search functionality ready for implementation");
}

// Keyboard navigation
function initializeKeyboardNavigation() {
  document.addEventListener("keydown", function (e) {
    // Escape key to close sidebar
    if (e.key === "Escape") {
      const sidebar = document.getElementById("sidebar");
      const sidebarToggle = document.getElementById("sidebarToggle");

      if (sidebar && sidebar.classList.contains("active")) {
        sidebar.classList.remove("active");
        if (sidebarToggle) {
          sidebarToggle.classList.remove("active");
          const icon = sidebarToggle.querySelector("i");
          icon.className = "fas fa-bars";
        }
      }
    }

    // Ctrl/Cmd + K for search (future feature)
    if ((e.ctrlKey || e.metaKey) && e.key === "k") {
      e.preventDefault();
      // Future search implementation
      console.log("Search shortcut pressed");
    }
  });
}

// Initialize keyboard navigation
initializeKeyboardNavigation();

// Performance optimization: Lazy loading for images (future implementation)
function initializeLazyLoading() {
  // This can be expanded later to add lazy loading for images
  console.log("Lazy loading ready for implementation");
}

// Theme switching functionality (future implementation)
function initializeThemeSwitcher() {
  // This can be expanded later to add dark/light theme switching
  console.log("Theme switcher ready for implementation");
}

// Export functions for global access
window.DocumentationApp = {
  scrollToTop,
  initializeSearch,
  initializeLazyLoading,
  initializeThemeSwitcher,
};
