/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-color);
  overflow-x: hidden;
}

/* Layout */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: var(--surface-color);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  transition: var(--transition);
  overflow-y: auto;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header h2 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background-color: var(--background-color);
  color: var(--text-primary);
}

.sidebar-content {
  padding: 1rem 0;
}

.nav-menu {
  list-style: none;
}

.nav-menu li {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
  border-left: 3px solid transparent;
}

.nav-link:hover {
  background-color: var(--background-color);
  color: var(--text-primary);
  border-left-color: var(--primary-color);
}

.nav-link.active {
  background-color: var(--primary-color);
  color: white;
  border-left-color: var(--primary-color);
}

.nav-link i {
  width: 1.25rem;
  text-align: center;
}

.main-content {
  margin-left: 280px;
  min-height: 100vh;
  transition: var(--transition);
}

.content-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
  padding: 3rem 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.header-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 1rem;
  z-index: 10;
}

.mobile-menu-toggle {
  display: none;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.mobile-menu-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.pdf-export-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.pdf-export-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.pdf-export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.pdf-export-btn i {
  font-size: 1rem;
}

.content-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  font-weight: 400;
}

/* Content Sections */
.content-section {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.section-header h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-subtitle {
  color: var(--text-secondary);
  font-style: italic;
}

.section-nav {
  display: flex;
  gap: 0.5rem;
}

/* Content Cards */
.content-card {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  padding: 2rem;
  margin-bottom: 2rem;
}

.lead {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.7;
}

/* Technology Stack */
.tech-stack h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.tech-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.tech-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.tech-icon.backend {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.tech-icon.frontend {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.tech-icon.database {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.tech-icon.payment {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.tech-icon.auth {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.tech-icon.file {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.tech-info h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.tech-info p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* System Features */
.system-features h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  transition: var(--transition);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
}

.feature-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.feature-card p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .sidebar-toggle {
    display: block;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .content-header {
    padding: 2rem 1rem;
  }

  .content-header h1 {
    font-size: 2rem;
  }

  .content-section {
    padding: 1rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .tech-item {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .content-header h1 {
    font-size: 1.75rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .content-card {
    padding: 1.5rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-section {
  animation: fadeInUp 0.6s ease-out;
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: var(--background-color);
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* User Roles & Capabilities Styles */
.roles-overview h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.role-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.role-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.role-card.admin::before {
  background: linear-gradient(90deg, #dc2626, #ef4444);
}

.role-card.guardian::before {
  background: linear-gradient(90deg, #059669, #10b981);
}

.role-card.coach::before {
  background: linear-gradient(90deg, #7c3aed, #8b5cf6);
}

.role-card.player::before {
  background: linear-gradient(90deg, #ea580c, #f97316);
}

.role-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.role-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.role-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.role-card.admin .role-icon {
  background: linear-gradient(135deg, #dc2626, #ef4444);
}

.role-card.guardian .role-icon {
  background: linear-gradient(135deg, #059669, #10b981);
}

.role-card.coach .role-icon {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}

.role-card.player .role-icon {
  background: linear-gradient(135deg, #ea580c, #f97316);
}

.role-header h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.role-badge {
  background: var(--background-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: auto;
}

.role-card p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.role-stats {
  display: flex;
  gap: 1rem;
}

.stat {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Role Details Styles */
.role-details h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.capability-section {
  margin-bottom: 3rem;
}

.capability-header {
  background: var(--background-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid var(--primary-color);
}

.capability-header.admin {
  border-left-color: #dc2626;
}

.capability-header.guardian {
  border-left-color: #059669;
}

.capability-header.coach {
  border-left-color: #7c3aed;
}

.capability-header.player {
  border-left-color: #ea580c;
}

.capability-header h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.capability-header p {
  color: var(--text-secondary);
  margin: 0;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.capability-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.capability-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.capability-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.capability-card h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.capability-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.capability-card li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
  line-height: 1.6;
}

.capability-card li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Role Comparison Table */
.role-comparison h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.comparison-table-wrapper {
  overflow-x: auto;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--surface-color);
  min-width: 600px;
}

.comparison-table th,
.comparison-table td {
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
}

.comparison-table th {
  background: var(--background-color);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.comparison-table th:first-child {
  text-align: left;
  font-weight: 600;
}

.comparison-table td:first-child {
  text-align: left;
  font-weight: 500;
  color: var(--text-primary);
}

.comparison-table tr:hover {
  background: var(--background-color);
}

.text-success {
  color: var(--success-color) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

/* Responsive adjustments for roles section */
@media (max-width: 768px) {
  .roles-grid {
    grid-template-columns: 1fr;
  }

  .capabilities-grid {
    grid-template-columns: 1fr;
  }

  .role-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .comparison-table-wrapper {
    margin: 0 -1rem;
  }

  .comparison-table {
    min-width: 500px;
  }
}

/* Data Flow Architecture Styles */
.flow-overview h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.flow-diagram {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.flow-step {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  width: 100%;
  max-width: 400px;
  text-align: center;
  transition: var(--transition);
  position: relative;
}

.flow-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.flow-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
}

.flow-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.flow-content p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
}

.flow-arrow {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin: 0.5rem 0;
}

/* Detailed Flow Diagrams */
.flow-details h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.flow-section {
  margin-bottom: 3rem;
}

.flow-header {
  background: var(--background-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid var(--primary-color);
}

.flow-header h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.flow-header p {
  color: var(--text-secondary);
  margin: 0;
}

.flow-diagram-detailed {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  overflow-x: auto;
  min-height: 200px;
}

.flow-node {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: var(--border-radius);
  min-width: 200px;
  flex-shrink: 0;
  transition: var(--transition);
}

.flow-node:hover {
  transform: scale(1.05);
}

.flow-node.start {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.flow-node.process {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
}

.flow-node.decision {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.flow-node.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.flow-node.end {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.node-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.node-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.node-content p {
  font-size: 0.75rem;
  opacity: 0.9;
  margin: 0;
}

.flow-connection {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.connection-line {
  width: 2rem;
  height: 2px;
  background: var(--border-color);
}

.connection-arrow {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.flow-branch {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  flex-shrink: 0;
}

.branch-yes,
.branch-no {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.branch-yes .connection-line {
  background: var(--success-color);
}

.branch-no .connection-line {
  background: var(--error-color);
}

/* Flow Summary */
.flow-summary h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.summary-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.summary-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.summary-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.summary-card p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.summary-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.summary-card li {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  padding-left: 1.25rem;
  position: relative;
}

.summary-card li::before {
  content: "→";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Responsive adjustments for flow diagrams */
@media (max-width: 768px) {
  .flow-diagram-detailed {
    flex-direction: column;
    align-items: stretch;
  }

  .flow-node {
    min-width: auto;
    width: 100%;
  }

  .flow-branch {
    width: 100%;
  }

  .branch-yes,
  .branch-no {
    width: 100%;
    justify-content: center;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .flow-step {
    max-width: none;
  }
}

/* Payment System Structure Styles */
.payment-overview h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.payment-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.payment-type-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.payment-type-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.payment-type-card.full-payment::before {
  background: linear-gradient(90deg, #059669, #10b981);
}

.payment-type-card.split-payment::before {
  background: linear-gradient(90deg, #7c3aed, #8b5cf6);
}

.payment-type-card.recurring-payment::before {
  background: linear-gradient(90deg, #ea580c, #f97316);
}

.payment-type-card.credit-payment::before {
  background: linear-gradient(90deg, #dc2626, #ef4444);
}

.payment-type-card.external-payment::before {
  background: linear-gradient(90deg, #0891b2, #06b6d4);
}

.payment-type-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.payment-type-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.payment-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.payment-type-card.full-payment .payment-icon {
  background: linear-gradient(135deg, #059669, #10b981);
}

.payment-type-card.split-payment .payment-icon {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}

.payment-type-card.recurring-payment .payment-icon {
  background: linear-gradient(135deg, #ea580c, #f97316);
}

.payment-type-card.credit-payment .payment-icon {
  background: linear-gradient(135deg, #dc2626, #ef4444);
}

.payment-type-card.external-payment .payment-icon {
  background: linear-gradient(135deg, #0891b2, #06b6d4);
}

.payment-type-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.payment-badge {
  background: var(--background-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: auto;
}

.payment-type-card p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.payment-type-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.payment-type-card li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
  font-size: 0.875rem;
}

.payment-type-card li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Coupon System Styles */
.coupon-system h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.coupon-overview {
  background: var(--background-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.coupon-info h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.coupon-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.coupon-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.coupon-feature i {
  color: var(--primary-color);
  width: 1rem;
  text-align: center;
}

.coupon-process h4,
.coupon-validation h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.process-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.step-number {
  width: 2rem;
  height: 2rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.validation-rules {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.validation-rule {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--surface-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.validation-rule i {
  color: var(--success-color);
  width: 1rem;
  text-align: center;
}

.validation-rule span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Payment Components Styles */
.payment-components h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.components-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.component-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.component-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.component-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.component-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.component-card p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.component-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.component-card li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  padding-left: 1.25rem;
  position: relative;
  font-size: 0.875rem;
}

.component-card li::before {
  content: "→";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Payment Workflow Styles */
.payment-workflow h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.workflow-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.workflow-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.workflow-step:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.workflow-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.workflow-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.workflow-content p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
}

/* Payment Methods Styles */
.payment-methods h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.method-workflows {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.method-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
}

.method-workflow:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.method-header {
  background: var(--background-color);
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.method-header h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.method-steps {
  padding: 1rem;
}

.method-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.method-step:last-child {
  border-bottom: none;
}

.method-step .step-number {
  width: 1.5rem;
  height: 1.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.method-step span:last-child {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive adjustments for payment system */
@media (max-width: 768px) {
  .payment-types-grid {
    grid-template-columns: 1fr;
  }

  .coupon-features {
    grid-template-columns: 1fr;
  }

  .process-steps {
    grid-template-columns: 1fr;
  }

  .validation-rules {
    grid-template-columns: 1fr;
  }

  .components-grid {
    grid-template-columns: 1fr;
  }

  .method-workflows {
    grid-template-columns: 1fr;
  }

  .workflow-step {
    flex-direction: column;
    text-align: center;
  }

  .workflow-icon {
    margin-bottom: 0.5rem;
  }
}

/* Invitation System Workflows Styles */
.invitation-overview h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.invitation-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.invitation-type-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.invitation-type-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.invitation-type-card.admin::before {
  background: linear-gradient(90deg, #dc2626, #ef4444);
}

.invitation-type-card.coach::before {
  background: linear-gradient(90deg, #7c3aed, #8b5cf6);
}

.invitation-type-card.guardian::before {
  background: linear-gradient(90deg, #059669, #10b981);
}

.invitation-type-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.invitation-type-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.invitation-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.invitation-type-card.admin .invitation-icon {
  background: linear-gradient(135deg, #dc2626, #ef4444);
}

.invitation-type-card.coach .invitation-icon {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}

.invitation-type-card.guardian .invitation-icon {
  background: linear-gradient(135deg, #059669, #10b981);
}

.invitation-type-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.invitation-badge {
  background: var(--background-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: auto;
}

.invitation-type-card p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.invitation-type-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.invitation-type-card li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
  font-size: 0.875rem;
}

.invitation-type-card li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Invitation Section Styles */
.invitation-section {
  margin-bottom: 3rem;
}

.invitation-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.invitation-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  overflow: hidden;
}

.workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.workflow-header h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.workflow-header p {
  color: var(--text-secondary);
  margin: 0;
}

.workflow-steps-detailed {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.workflow-step-detailed {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.workflow-step-detailed:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.step-number-large {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  flex-shrink: 0;
}

.step-content-detailed h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.step-content-detailed p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Invitation Tracking Styles */
.invitation-tracking h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tracking-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.tracking-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.tracking-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.tracking-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.tracking-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.status-list,
.notification-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.status-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-item.pending .status-dot {
  background: var(--warning-color);
}

.status-item.accepted .status-dot {
  background: var(--success-color);
}

.status-item.declined .status-dot {
  background: var(--error-color);
}

.status-item.expired .status-dot {
  background: var(--text-muted);
}

.status-item span:last-child {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.notification-item i {
  color: var(--primary-color);
  width: 1rem;
  text-align: center;
}

.notification-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive adjustments for invitation system */
@media (max-width: 768px) {
  .invitation-types-grid {
    grid-template-columns: 1fr;
  }

  .workflow-steps-detailed {
    grid-template-columns: 1fr;
  }

  .tracking-overview {
    grid-template-columns: 1fr;
  }

  .workflow-step-detailed {
    flex-direction: column;
    text-align: center;
  }

  .step-number-large {
    align-self: center;
  }
}

/* Multi-Role User Management Styles */
.role-switching-overview h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.role-switching-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.role-switching-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.role-switching-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.role-switching-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.role-switching-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.role-switching-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.role-switching-card p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.role-switching-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.role-switching-card li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
  font-size: 0.875rem;
}

.role-switching-card li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Role Switching Section Styles */
.role-switching-section {
  margin-bottom: 3rem;
}

.role-switching-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.switching-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  overflow: hidden;
}

.switching-steps {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.switching-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.switching-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Role Comparison Styles */
.role-comparison h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.role-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.role-features-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.role-features-card.guardian {
  border-left: 4px solid #059669;
}

.role-features-card.coach {
  border-left: 4px solid #7c3aed;
}

.role-features-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.role-features-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.role-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
  flex-shrink: 0;
}

.role-features-card.guardian .role-icon {
  background: linear-gradient(135deg, #059669, #10b981);
}

.role-features-card.coach .role-icon {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}

.role-features-header h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.feature-item i {
  color: var(--primary-color);
  width: 1rem;
  text-align: center;
}

.feature-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Data Isolation Section Styles */
.data-isolation-section {
  margin-bottom: 3rem;
}

.data-isolation-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.isolation-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.isolation-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.isolation-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.isolation-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.isolation-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.isolation-card p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.access-levels {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.access-level {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.level-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.level-desc {
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.visibility-rules {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.visibility-rule {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.rule-icon {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: white;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.visibility-rule:nth-child(1) .rule-icon,
.visibility-rule:nth-child(2) .rule-icon {
  background: var(--success-color);
}

.visibility-rule:nth-child(3) .rule-icon {
  background: var(--error-color);
}

.rule-content h6 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.rule-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Multi-Role Workflows Styles */
.multi-role-workflows h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.workflow-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.workflow-example {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.example-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.example-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.example-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.scenario-steps {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.scenario-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.scenario-step:hover {
  transform: translateX(4px);
  border-color: var(--primary-color);
}

.step-number {
  width: 2rem;
  height: 2rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.step-details h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.step-details p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.sync-flow {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sync-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.sync-item:hover {
  transform: translateX(4px);
  border-color: var(--primary-color);
}

.sync-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.sync-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.sync-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Implementation Details Styles */
.implementation-details h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.implementation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.implementation-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.implementation-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.implementation-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.implementation-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.implementation-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.impl-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.impl-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.impl-desc {
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.5;
}

/* Responsive adjustments for multi-role management */
@media (max-width: 768px) {
  .role-switching-grid {
    grid-template-columns: 1fr;
  }

  .switching-steps {
    grid-template-columns: 1fr;
  }

  .role-features-grid {
    grid-template-columns: 1fr;
  }

  .isolation-overview {
    grid-template-columns: 1fr;
  }

  .workflow-examples {
    grid-template-columns: 1fr;
  }

  .implementation-grid {
    grid-template-columns: 1fr;
  }

  .switching-step,
  .scenario-step,
  .sync-item {
    flex-direction: column;
    text-align: center;
  }

  .step-icon,
  .step-number,
  .sync-icon {
    align-self: center;
  }
}

/* Program Management Workflows Styles */
.program-creation-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.program-specs-overview h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.program-specs-overview p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.specs-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.specs-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.specs-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.specs-card h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.specs-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.spec-item {
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.spec-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Eligibility Section Styles */
.eligibility-section {
  margin-bottom: 3rem;
}

.eligibility-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.eligibility-overview h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.eligibility-overview p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.eligibility-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  overflow: hidden;
}

.eligibility-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.eligibility-workflow .workflow-header h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.eligibility-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.filter-criteria,
.eligibility-checks,
.display-logic {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.criteria-item,
.check-item,
.logic-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.criteria-item:hover,
.check-item:hover,
.logic-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.criteria-icon,
.check-icon,
.logic-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.criteria-content h6,
.check-content h6,
.logic-content h6 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.criteria-content p,
.logic-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.code-block {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-top: 0.5rem;
}

.code-block pre {
  margin: 0;
  font-size: 0.75rem;
  line-height: 1.4;
  color: var(--text-primary);
  overflow-x: auto;
}

.code-block code {
  font-family: "Courier New", monospace;
  background: none;
  padding: 0;
}

/* Registration Eligibility Section Styles */
.registration-eligibility-section {
  margin-bottom: 3rem;
}

.registration-eligibility-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.registration-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.registration-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.registration-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.registration-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.validation-steps {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.validation-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.validation-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.validation-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.validation-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.validation-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.registration-process {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.registration-process h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.process-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.process-step .step-number {
  width: 2rem;
  height: 2rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.process-step .step-details h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.4;
}

/* Program Types Section Styles */
.program-types-section {
  margin-bottom: 3rem;
}

.program-types-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.program-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.program-type-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.program-type-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.program-type-card.individual::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.program-type-card.aau::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.program-type-card.tryout::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.program-type-card.team::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.program-type-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.program-type-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.program-type-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.program-type-card.individual .program-type-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.program-type-card.aau .program-type-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.program-type-card.tryout .program-type-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.program-type-card.team .program-type-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.program-type-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.program-type-badge {
  background: var(--background-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: auto;
}

.program-type-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.detail-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.detail-value {
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.5;
}

/* Lifecycle Section Styles */
.lifecycle-section {
  margin-bottom: 3rem;
}

.lifecycle-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.lifecycle-phases {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.phase-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
}

.phase-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.phase-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.phase-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
  flex-shrink: 0;
}

.phase-card.creation .phase-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.phase-card.active .phase-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.phase-card.execution .phase-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.phase-card.completion .phase-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.phase-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.phase-steps {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.phase-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.phase-step:hover {
  transform: translateX(4px);
  border-color: var(--primary-color);
}

.phase-step .step-number {
  width: 2rem;
  height: 2rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.phase-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.phase-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Responsive adjustments for program management */
@media (max-width: 768px) {
  .specs-grid {
    grid-template-columns: 1fr;
  }

  .filter-criteria,
  .eligibility-checks,
  .display-logic {
    grid-template-columns: 1fr;
  }

  .validation-steps {
    grid-template-columns: 1fr;
  }

  .process-steps {
    grid-template-columns: 1fr;
  }

  .program-types-grid {
    grid-template-columns: 1fr;
  }

  .lifecycle-phases {
    grid-template-columns: 1fr;
  }

  .criteria-item,
  .check-item,
  .logic-item,
  .validation-step,
  .process-step,
  .phase-step {
    flex-direction: column;
    text-align: center;
  }

  .criteria-icon,
  .check-icon,
  .logic-icon,
  .validation-step .step-icon,
  .process-step .step-number,
  .phase-step .step-number {
    align-self: center;
  }
}

/* Team Management System Styles */
.team-creation-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.team-creation-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.creation-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.creation-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.creation-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.creation-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.creation-steps {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.creation-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.creation-step:hover {
  transform: translateX(4px);
  border-color: var(--primary-color);
}

.creation-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.creation-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.creation-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.team-info-cards {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.info-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.info-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.info-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.info-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.info-value {
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.5;
}

/* Player Management Section Styles */
.player-management-section {
  margin-bottom: 3rem;
}

.player-management-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.player-management-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.management-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.management-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.management-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.management-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.management-steps {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.management-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.management-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.management-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.management-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.management-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.player-roster-features {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.roster-feature {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.roster-feature:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.feature-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.roster-feature h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.feature-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.detail-item i {
  color: var(--success-color);
  width: 1rem;
  text-align: center;
}

.detail-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Coach Assignment Section Styles */
.coach-assignment-section {
  margin-bottom: 3rem;
}

.coach-assignment-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.coach-assignment-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.assignment-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.assignment-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.assignment-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.assignment-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.assignment-steps {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.assignment-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.assignment-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.assignment-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.assignment-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.assignment-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.coach-roles {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.role-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.role-card.primary {
  border-left: 4px solid #f59e0b;
}

.role-card.assistant {
  border-left: 4px solid #8b5cf6;
}

.role-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.role-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.role-card.primary .role-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.role-card.assistant .role-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.role-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.role-permissions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.permission-item i {
  width: 1rem;
  text-align: center;
}

.permission-item i.fa-check {
  color: var(--success-color);
}

.permission-item i.fa-times {
  color: var(--error-color);
}

.permission-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Team-Program Relationships Section Styles */
.team-program-section {
  margin-bottom: 3rem;
}

.team-program-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.relationship-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.relationship-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.relationship-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.relationship-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.relationship-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.relationship-steps {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.relationship-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.relationship-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.relationship-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.relationship-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.relationship-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.program-requirements {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.requirement-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.requirement-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.requirement-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.requirement-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.requirement-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.requirement-item i {
  color: var(--success-color);
  width: 1rem;
  text-align: center;
}

.requirement-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Team Performance Section Styles */
.team-performance-section {
  margin-bottom: 3rem;
}

.team-performance-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.performance-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.metric-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.metric-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.metric-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.metric-value {
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.5;
}

/* Responsive adjustments for team management */
@media (max-width: 768px) {
  .team-creation-overview,
  .player-management-overview,
  .coach-assignment-overview,
  .relationship-overview {
    grid-template-columns: 1fr;
  }

  .creation-steps {
    grid-template-columns: 1fr;
  }

  .management-steps,
  .assignment-steps,
  .relationship-steps {
    grid-template-columns: 1fr;
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }

  .creation-step,
  .management-step,
  .assignment-step,
  .relationship-step {
    flex-direction: column;
    text-align: center;
  }

  .creation-step .step-icon,
  .management-step .step-icon,
  .assignment-step .step-icon,
  .relationship-step .step-icon {
    align-self: center;
  }
}

/* Reporting & Analytics Styles */
.admin-reports-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reports-overview {
  margin-bottom: 3rem;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.report-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.report-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.report-card.user-reports::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.report-card.program-reports::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.report-card.financial-reports::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.report-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.report-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.report-card.user-reports .report-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.report-card.program-reports .report-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.report-card.financial-reports .report-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.report-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.report-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.report-badge {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.report-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.metric-item i {
  color: var(--primary-color);
  width: 1rem;
  text-align: center;
}

.metric-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.report-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.feature-item i {
  color: var(--success-color);
  width: 1rem;
  text-align: center;
  font-size: 0.75rem;
}

.feature-item span {
  color: var(--text-secondary);
  font-size: 0.75rem;
}

/* Data Export Section Styles */
.data-export-section {
  margin-bottom: 3rem;
}

.data-export-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.export-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.export-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.export-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.export-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.export-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.export-steps {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.export-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.export-step:hover {
  transform: translateX(4px);
  border-color: var(--primary-color);
}

.export-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.export-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.export-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.export-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.export-type-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.export-type-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.export-type-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.export-type-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.export-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.export-details .detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.export-details .detail-item i {
  color: var(--success-color);
  width: 1rem;
  text-align: center;
}

.export-details .detail-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Report Filtering Section Styles */
.report-filtering-section {
  margin-bottom: 3rem;
}

.report-filtering-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filtering-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.filter-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.filter-category {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.filter-category:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.filter-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.filter-category h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.filter-option i {
  color: var(--primary-color);
  width: 1rem;
  text-align: center;
}

.filter-option span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Analytics Dashboard Section Styles */
.analytics-dashboard-section {
  margin-bottom: 3rem;
}

.analytics-dashboard-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dashboard-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.dashboard-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.dashboard-metrics .metric-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.dashboard-metrics .metric-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.dashboard-metrics .metric-card.key-metrics::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.dashboard-metrics .metric-card.trend-analysis::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.dashboard-metrics .metric-card.data-visualization::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.dashboard-metrics .metric-card.real-time-monitoring::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.dashboard-metrics .metric-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.dashboard-metrics .metric-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.dashboard-metrics .metric-card.key-metrics .metric-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.dashboard-metrics .metric-card.trend-analysis .metric-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.dashboard-metrics .metric-card.data-visualization .metric-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.dashboard-metrics .metric-card.real-time-monitoring .metric-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.dashboard-metrics .metric-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.dashboard-metrics .metric-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.kpi-item,
.trend-item,
.visualization-item,
.monitoring-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.kpi-label,
.trend-label,
.viz-label,
.monitor-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.kpi-value,
.trend-value,
.viz-value,
.monitor-value {
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.5;
}

/* Responsive adjustments for reporting & analytics */
@media (max-width: 768px) {
  .reports-grid {
    grid-template-columns: 1fr;
  }

  .export-overview {
    grid-template-columns: 1fr;
  }

  .export-steps {
    grid-template-columns: 1fr;
  }

  .export-types {
    grid-template-columns: 1fr;
  }

  .filter-categories {
    grid-template-columns: 1fr;
  }

  .dashboard-metrics {
    grid-template-columns: 1fr;
  }

  .export-step {
    flex-direction: column;
    text-align: center;
  }

  .export-step .step-icon {
    align-self: center;
  }
}

/* Security & Access Control Styles */
.authentication-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.auth-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.auth-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.auth-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.auth-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.auth-steps {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.auth-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.auth-step:hover {
  transform: translateX(4px);
  border-color: var(--primary-color);
}

.auth-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.auth-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.auth-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.auth-features {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.auth-feature-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.auth-feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.auth-feature-card .feature-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.auth-feature-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.auth-feature-card .feature-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.auth-feature-card .detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.auth-feature-card .detail-item i {
  color: var(--success-color);
  width: 1rem;
  text-align: center;
}

.auth-feature-card .detail-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Authorization Section Styles */
.authorization-section {
  margin-bottom: 3rem;
}

.authorization-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.authorization-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.auth-roles {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.role-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.role-card.admin-role {
  border-left: 4px solid #f59e0b;
}

.role-card.guardian-role {
  border-left: 4px solid #3b82f6;
}

.role-card.coach-role {
  border-left: 4px solid #10b981;
}

.role-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.role-card .role-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.role-card.admin-role .role-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.role-card.guardian-role .role-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.role-card.coach-role .role-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.role-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.permission-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.permission-item i {
  width: 1rem;
  text-align: center;
}

.permission-item i.fa-check {
  color: var(--success-color);
}

.permission-item i.fa-times {
  color: var(--error-color);
}

.permission-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.access-control {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.control-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.control-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.control-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.control-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.control-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.control-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.control-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.control-value {
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.5;
}

/* Security Features Section Styles */
.security-features-section {
  margin-bottom: 3rem;
}

.security-features-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.security-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.security-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.security-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.security-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.security-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.security-steps {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.security-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.security-step:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.security-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.security-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.security-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.security-measures {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.measure-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.measure-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.measure-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.measure-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.measure-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.measure-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.measure-item i {
  color: var(--success-color);
  width: 1rem;
  text-align: center;
}

.measure-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Data Protection Section Styles */
.data-protection-section {
  margin-bottom: 3rem;
}

.data-protection-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.protection-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.protection-workflow {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.protection-workflow .workflow-header {
  background: var(--background-color);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.protection-workflow .workflow-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.protection-workflow .workflow-header p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.protection-steps {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.protection-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.protection-step:hover {
  transform: translateX(4px);
  border-color: var(--primary-color);
}

.protection-step .step-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.protection-step .step-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.protection-step .step-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.protection-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.protection-feature-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.protection-feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.protection-feature-card .feature-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

.protection-feature-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.protection-feature-card .feature-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.protection-feature-card .detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.protection-feature-card .detail-item i {
  color: var(--success-color);
  width: 1rem;
  text-align: center;
}

.protection-feature-card .detail-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive adjustments for security & access control */
@media (max-width: 768px) {
  .auth-overview,
  .authorization-overview,
  .security-overview,
  .protection-overview {
    grid-template-columns: 1fr;
  }

  .security-steps {
    grid-template-columns: 1fr;
  }

  .security-measures,
  .protection-features {
    grid-template-columns: 1fr;
  }

  .auth-step,
  .protection-step {
    flex-direction: column;
    text-align: center;
  }

  .auth-step .step-icon,
  .protection-step .step-icon {
    align-self: center;
  }
}

/* Print Styles for PDF Export */
@media print {
  /* Hide navigation and controls */
  .sidebar,
  .header-controls,
  .section-nav,
  .mobile-menu-toggle,
  .pdf-export-btn {
    display: none !important;
  }

  /* Adjust main content for print */
  .main-content {
    margin-left: 0 !important;
    width: 100% !important;
  }

  /* Optimize content for print */
  .content-header {
    padding: 2rem 1rem !important;
    page-break-after: avoid;
  }

  .content-section {
    page-break-inside: avoid;
    margin-bottom: 2rem !important;
  }

  .section-header {
    page-break-after: avoid;
  }

  /* Ensure good contrast for print */
  .content-card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }

  /* Optimize tables for print */
  .comparison-table-wrapper {
    overflow: visible !important;
  }

  .comparison-table {
    font-size: 0.8rem !important;
  }

  /* Optimize flow diagrams for print */
  .flow-diagram-detailed {
    flex-direction: column !important;
    align-items: stretch !important;
  }

  .flow-node {
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  /* Hide interactive elements */
  .btn,
  button {
    display: none !important;
  }

  /* Optimize colors for print */
  .role-card.admin,
  .capability-header.admin {
    background: #f8f9fa !important;
    border-left: 4px solid #2563eb !important;
  }

  .role-card.guardian,
  .capability-header.guardian {
    background: #f8f9fa !important;
    border-left: 4px solid #10b981 !important;
  }

  .role-card.coach,
  .capability-header.coach {
    background: #f8f9fa !important;
    border-left: 4px solid #f59e0b !important;
  }

  .role-card.player,
  .capability-header.player {
    background: #f8f9fa !important;
    border-left: 4px solid #8b5cf6 !important;
  }

  /* Ensure text is readable */
  body {
    color: #000 !important;
    background: #fff !important;
  }

  /* Page breaks */
  h1,
  h2,
  h3 {
    page-break-after: avoid;
  }

  .content-section {
    page-break-before: auto;
  }
}
